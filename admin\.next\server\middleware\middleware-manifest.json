{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_35401bb2._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_b36fea90.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/authors/:path*{(\\\\.json)}?", "originalSource": "/authors/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/works/:path*{(\\\\.json)}?", "originalSource": "/works/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "jIBJsTS5W+Q9SPDF+Y9qVlmAvdl4pgmfLaawoITWOIU=", "__NEXT_PREVIEW_MODE_ID": "d1491a58462688fbc6f621d8605594c7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4dc5288babc5840c4ed800978d0bc159ff88b9f6f8c0cf65aa192a9514fea6eb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d26aa150f42dfbeda362a85de0e98852011fcfa9887a3783bb116656a49a81c0"}}}, "instrumentation": null, "functions": {}}