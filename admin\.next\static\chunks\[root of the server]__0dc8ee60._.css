/* [next]/internal/font/google/gyByhwUxId8gMEwYGFWNOITddY4-s.woff2 (static in css) */
/* embedded static asset "/_next/static/media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2" */

/* [next]/internal/font/google/gyByhwUxId8gMEwSGFWNOITddY4-s.woff2 (static in css) */
/* embedded static asset "/_next/static/media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2" */

/* [next]/internal/font/google/gyByhwUxId8gMEwcGFWNOITd-s.p.woff2 (static in css) */
/* embedded static asset "/_next/static/media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2" */

/* [next]/internal/font/google/geist_e531dabc.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310ad.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a5b.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_e531dabc-module__QGiZLq__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_e531dabc-module__QGiZLq__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}


/* [next]/internal/font/google/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.woff2 (static in css) */
/* embedded static asset "/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2" */

/* [next]/internal/font/google/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.woff2 (static in css) */
/* embedded static asset "/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2" */

/* [next]/internal/font/google/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.woff2 (static in css) */
/* embedded static asset "/_next/static/media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2" */

/* [next]/internal/font/google/geist_mono_68a01160.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkjkotbA-s.cb6bbcb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkjkotbA-s.e32db976.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_68a01160-module__YLcDdW__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_68a01160-module__YLcDdW__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties;
@layer theme, base, components, utilities;

@layer theme {
  :root, :host {
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-900: oklch(39.6% .141 25.723);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-slate-50: oklch(98.4% .003 247.858);
    --color-slate-200: oklch(92.9% .013 255.508);
    --color-slate-300: oklch(86.9% .022 252.894);
    --color-slate-500: oklch(55.4% .046 257.417);
    --color-slate-600: oklch(44.6% .043 257.281);
    --color-slate-700: oklch(37.2% .044 257.287);
    --color-slate-800: oklch(27.9% .041 260.031);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --tracking-tight: -.025em;
    --tracking-widest: .1em;
    --radius-xs: .125rem;
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }

  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: #0000;
    opacity: 1;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer utilities {
  .\@container\/card-header {
    container-type: inline-size;
    container-name: card-header;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .start-1 {
    inset-inline-start: calc(var(--spacing) * 1);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-px {
    top: -1px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1 {
    top: calc(var(--spacing) * 1);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-\[5px\] {
    top: 5px;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-full {
    top: 100%;
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .-right-3 {
    right: calc(var(--spacing) * -3);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-0\.5 {
    right: calc(var(--spacing) * .5);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-\[-1\.5px\] {
    right: -1.5px;
  }

  .right-\[-11px\] {
    right: -11px;
  }

  .right-\[28px\] {
    right: 28px;
  }

  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }

  .-bottom-1\.5 {
    bottom: calc(var(--spacing) * -1.5);
  }

  .-bottom-px {
    bottom: -1px;
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }

  .bottom-2 {
    bottom: calc(var(--spacing) * 2);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .-left-0\.5 {
    left: calc(var(--spacing) * -.5);
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .-left-3 {
    left: calc(var(--spacing) * -3);
  }

  .-left-6 {
    left: calc(var(--spacing) * -6);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-2\.5 {
    left: calc(var(--spacing) * 2.5);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-\[-1\.5px\] {
    left: -1.5px;
  }

  .left-\[-10\.5px\] {
    left: -10.5px;
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .z-1 {
    z-index: 1;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-51 {
    z-index: 51;
  }

  .z-100 {
    z-index: 100;
  }

  .z-500 {
    z-index: 500;
  }

  .z-\[9999\] {
    z-index: 9999;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }

  .mx-1\.5 {
    margin-inline: calc(var(--spacing) * 1.5);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mx-px {
    margin-inline: 1px;
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-1\.5 {
    margin-block: calc(var(--spacing) * 1.5);
  }

  .my-auto {
    margin-block: auto;
  }

  .my-px {
    margin-block: 1px;
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-\[0\.75em\] {
    margin-top: .75em;
  }

  .mt-\[1\.4em\] {
    margin-top: 1.4em;
  }

  .mt-\[1\.6em\] {
    margin-top: 1.6em;
  }

  .mt-\[1em\] {
    margin-top: 1em;
  }

  .-mr-3 {
    margin-right: calc(var(--spacing) * -3);
  }

  .mr-0 {
    margin-right: calc(var(--spacing) * 0);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-auto {
    margin-right: auto;
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .-ml-2 {
    margin-left: calc(var(--spacing) * -2);
  }

  .-ml-3 {
    margin-left: calc(var(--spacing) * -3);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-auto {
    margin-left: auto;
  }

  .ml-px {
    margin-left: 1px;
  }

  .box-border {
    box-sizing: border-box;
  }

  .box-content {
    box-sizing: content-box;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .flex\! {
    display: flex !important;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .\!size-3 {
    width: calc(var(--spacing) * 3) !important;
    height: calc(var(--spacing) * 3) !important;
  }

  .\!size-3\.5 {
    width: calc(var(--spacing) * 3.5) !important;
    height: calc(var(--spacing) * 3.5) !important;
  }

  .size-0 {
    width: calc(var(--spacing) * 0);
    height: calc(var(--spacing) * 0);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\! {
    width: calc(var(--spacing) * 3) !important;
    height: calc(var(--spacing) * 3) !important;
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .size-6 {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-10 {
    width: calc(var(--spacing) * 10);
    height: calc(var(--spacing) * 10);
  }

  .size-\[28px\] {
    width: 28px;
    height: 28px;
  }

  .size-\[130px\] {
    width: 130px;
    height: 130px;
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-\[1\.2rem\] {
    height: 1.2rem;
  }

  .h-\[1\.3em\] {
    height: 1.3em;
  }

  .h-\[1\.5em\] {
    height: 1.5em;
  }

  .h-\[19px\] {
    height: 19px;
  }

  .h-\[23rem\] {
    height: 23rem;
  }

  .h-\[24px\] {
    height: 24px;
  }

  .h-\[28px\] {
    height: 28px;
  }

  .h-\[344px\] {
    height: 344px;
  }

  .h-\[600px\] {
    height: 600px;
  }

  .h-\[650px\] {
    height: 650px;
  }

  .h-\[calc\(100\%-1px\)\] {
    height: calc(100% - 1px);
  }

  .h-\[calc\(100\%_\+_8px\)\] {
    height: calc(100% + 8px);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-14 {
    max-height: calc(var(--spacing) * 14);
  }

  .max-h-\[50vh\] {
    max-height: 50vh;
  }

  .max-h-\[288px\] {
    max-height: 288px;
  }

  .max-h-\[300px\] {
    max-height: 300px;
  }

  .max-h-\[500px\] {
    max-height: 500px;
  }

  .max-h-\[calc\(100vh-4rem\)\] {
    max-height: calc(100vh - 4rem);
  }

  .max-h-\[min\(50dvh\,calc\(-24px\+var\(--radix-popper-available-height\)\)\)\] {
    max-height: min(50dvh, calc(-24px + var(--radix-popper-available-height)));
  }

  .max-h-\[min\(70vh\,320px\)\] {
    max-height: min(70vh, 320px);
  }

  .max-h-screen {
    max-height: 100vh;
  }

  .min-h-14 {
    min-height: calc(var(--spacing) * 14);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[1lh\] {
    min-height: 1lh;
  }

  .min-h-\[25px\] {
    min-height: 25px;
  }

  .min-h-\[50\%\] {
    min-height: 50%;
  }

  .min-h-\[100px\] {
    min-height: 100px;
  }

  .min-h-\[400px\] {
    min-height: 400px;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0\.5 {
    width: calc(var(--spacing) * .5);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-4\.5 {
    width: calc(var(--spacing) * 4.5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-\[1\.2rem\] {
    width: 1.2rem;
  }

  .w-\[180px\] {
    width: 180px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[330px\] {
    width: 330px;
  }

  .w-\[380px\] {
    width: 380px;
  }

  .w-\[min\(100\%\,600px\)\] {
    width: min(100%, 600px);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-px {
    width: 1px;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-\[80vw\] {
    max-width: 80vw;
  }

  .max-w-\[700px\] {
    max-width: 700px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-\[calc\(100vw-24px\)\] {
    max-width: calc(100vw - 24px);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[92px\] {
    min-width: 92px;
  }

  .min-w-\[125px\] {
    min-width: 125px;
  }

  .min-w-\[130px\] {
    min-width: 130px;
  }

  .min-w-\[180px\] {
    min-width: 180px;
  }

  .min-w-\[220px\] {
    min-width: 220px;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .table-fixed {
    table-layout: fixed;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-2 {
    --tw-translate-x: calc(var(--spacing) * -2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-0 {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-0 {
    rotate: none;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-in {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-col-resize {
    cursor: col-resize;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-row-resize {
    cursor: row-resize;
  }

  .cursor-text {
    cursor: text;
  }

  .resize {
    resize: both;
  }

  .resize-none {
    resize: none;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .list-none {
    list-style-type: none;
  }

  .appearance-none {
    appearance: none;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .grid-cols-\[repeat\(10\,1fr\)\] {
    grid-template-columns: repeat(10, 1fr);
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .place-items-center {
    place-items: center;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-evenly {
    justify-content: space-evenly;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-10 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-\[50\%\] {
    border-radius: 50%;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-t-lg {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .rounded-r-md {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .rounded-r-none {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .border-border {
    border-color: var(--border);
  }

  .border-current {
    border-color: currentColor;
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-muted {
    border-color: var(--muted);
  }

  .border-primary {
    border-color: var(--primary);
  }

  .border-slate-200 {
    border-color: var(--color-slate-200);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-b-border {
    border-bottom-color: var(--border);
  }

  .border-b-gray-300 {
    border-bottom-color: var(--color-gray-300);
  }

  .border-b-gray-500 {
    border-bottom-color: var(--color-gray-500);
  }

  .border-b-purple-100 {
    border-bottom-color: var(--color-purple-100);
  }

  .bg-\(--cellBackground\) {
    background-color: var(--cellBackground);
  }

  .bg-\[rgba\(0\,0\,0\,0\.5\)\] {
    background-color: #00000080;
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-background\/95 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/95 {
      background-color: color-mix(in oklab, var(--background) 95%, transparent);
    }
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-black\/80 {
    background-color: #000c;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/80 {
      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);
    }
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-gray-300\/25 {
    background-color: #d1d5dc40;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-300\/25 {
      background-color: color-mix(in oklab, var(--color-gray-300) 25%, transparent);
    }
  }

  .bg-gray-400\/25 {
    background-color: #99a1af40;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-gray-400\/25 {
      background-color: color-mix(in oklab, var(--color-gray-400) 25%, transparent);
    }
  }

  .bg-inherit {
    background-color: inherit;
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-muted\/60 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/60 {
      background-color: color-mix(in oklab, var(--muted) 60%, transparent);
    }
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-popover\/90 {
    background-color: var(--popover);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-popover\/90 {
      background-color: color-mix(in oklab, var(--popover) 90%, transparent);
    }
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary\/40 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/40 {
      background-color: color-mix(in oklab, var(--primary) 40%, transparent);
    }
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-ring {
    background-color: var(--ring);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-slate-50 {
    background-color: var(--color-slate-50);
  }

  .bg-slate-700 {
    background-color: var(--color-slate-700);
  }

  .bg-slate-800 {
    background-color: var(--color-slate-800);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-clip-content {
    background-clip: content-box;
  }

  .bg-center {
    background-position: center;
  }

  .fill-current {
    fill: currentColor;
  }

  .stroke-\[3px\] {
    stroke-width: 3px;
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-\[3px\] {
    padding: 3px;
  }

  .p-px {
    padding: 1px;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-0\.5 {
    padding-inline: calc(var(--spacing) * .5);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .px-16 {
    padding-inline: calc(var(--spacing) * 16);
  }

  .px-\[0\.3em\] {
    padding-inline: .3em;
  }

  .px-px {
    padding-inline: 1px;
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-\[0\.2em\] {
    padding-block: .2em;
  }

  .py-\[1\.5px\] {
    padding-block: 1.5px;
  }

  .py-\[3px\] {
    padding-block: 3px;
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-0\.5 {
    padding-top: calc(var(--spacing) * .5);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-1\.5 {
    padding-top: calc(var(--spacing) * 1.5);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-\[2px\] {
    padding-top: 2px;
  }

  .pt-\[3px\] {
    padding-top: 3px;
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-9 {
    padding-right: calc(var(--spacing) * 9);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-72 {
    padding-bottom: calc(var(--spacing) * 72);
  }

  .pb-\[51\.25\%\] {
    padding-bottom: 51.25%;
  }

  .pb-\[56\.25\%\] {
    padding-bottom: 56.25%;
  }

  .pb-\[56\.0417\%\] {
    padding-bottom: 56.0417%;
  }

  .pb-\[75\%\] {
    padding-bottom: 75%;
  }

  .pb-px {
    padding-bottom: 1px;
  }

  .pl-0\.5 {
    padding-left: calc(var(--spacing) * .5);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-\[26px\] {
    padding-left: 26px;
  }

  .pl-\[32px\] {
    padding-left: 32px;
  }

  .pl-\[50px\] {
    padding-left: 50px;
  }

  .text-center {
    text-align: center;
  }

  .text-justify {
    text-align: justify;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-baseline {
    vertical-align: baseline;
  }

  .align-middle {
    vertical-align: middle;
  }

  .align-text-bottom {
    vertical-align: text-bottom;
  }

  .font-\[inherit\] {
    font-family: inherit;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .text-\[1\.1em\] {
    font-size: 1.1em;
  }

  .text-\[1\.5em\] {
    font-size: 1.5em;
  }

  .text-\[1\.25em\] {
    font-size: 1.25em;
  }

  .text-\[1\.875em\] {
    font-size: 1.875em;
  }

  .leading-\[normal\] {
    --tw-leading: normal;
    line-height: normal;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-accent-foreground {
    color: var(--accent-foreground);
  }

  .text-background {
    color: var(--background);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-current {
    color: currentColor;
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-inherit {
    color: inherit;
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-muted-foreground\/70 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/70 {
      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);
    }
  }

  .text-muted-foreground\/80 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/80 {
      color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
    }
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-slate-300 {
    color: var(--color-slate-300);
  }

  .text-slate-500 {
    color: var(--color-slate-500);
  }

  .text-slate-600 {
    color: var(--color-slate-600);
  }

  .text-white {
    color: var(--color-white);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .italic {
    font-style: italic;
  }

  .line-through {
    text-decoration-line: line-through;
  }

  .no-underline {
    text-decoration-line: none;
  }

  .underline {
    text-decoration-line: underline;
  }

  .decoration-primary {
    text-decoration-color: var(--primary);
  }

  .decoration-\[0\.5px\] {
    text-decoration-thickness: .5px;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .caret-primary {
    caret-color: var(--primary);
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[rgba\(255\,_255\,_255\,_0\.1\)_0px_0\.5px_0px_0px_inset\,_rgb\(248\,_249\,_250\)_0px_1px_5px_0px_inset\,_rgb\(193\,_200\,_205\)_0px_0px_0px_0\.5px\,_rgb\(193\,_200\,_205\)_0px_2px_1px_-1px\,_rgb\(193\,_200\,_205\)_0px_1px_0px_0px\] {
    --tw-shadow-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-\[rgba\(255\,_255\,_255\,_0\.1\)_0px_0\.5px_0px_0px_inset\,_rgb\(248\,_249\,_250\)_0px_1px_5px_0px_inset\,_rgb\(193\,_200\,_205\)_0px_0px_0px_0\.5px\,_rgb\(193\,_200\,_205\)_0px_2px_1px_-1px\,_rgb\(193\,_200\,_205\)_0px_1px_0px_0px\] {
      --tw-shadow-color: color-mix(in oklab, #ffffff1a 0px .5px 0px 0px inset, #f8f9fa 0px 1px 5px 0px inset, #c1c8cd 0px 0px 0px .5px, #c1c8cd 0px 2px 1px -1px, #c1c8cd 0px 1px 0px 0px var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-ring {
    --tw-ring-color: var(--ring);
  }

  .ring-offset-2 {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .\!filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, ) !important;
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xs {
    --tw-backdrop-blur: blur(var(--blur-xs));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-75 {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .\[contain\:content\] {
    contain: content;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .select-text {
    -webkit-user-select: text;
    user-select: text;
  }

  .\[tab-size\:2\] {
    tab-size: 2;
  }

  .fade-in {
    --tw-enter-opacity: 0;
  }

  .fade-in-80 {
    --tw-enter-opacity: .8;
  }

  .running {
    animation-play-state: running;
  }

  :is(.\*\:m-0 > *) {
    margin: calc(var(--spacing) * 0);
  }

  .not-last\:border-b:not(:last-child) {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .group-first\/column\:-left-1:is(:where(.group\/column):first-child *) {
    left: calc(var(--spacing) * -1);
  }

  .group-first\/column\:pl-0:is(:where(.group\/column):first-child *) {
    padding-left: calc(var(--spacing) * 0);
  }

  .group-last\/column\:-right-1:is(:where(.group\/column):last-child *) {
    right: calc(var(--spacing) * -1);
  }

  .group-last\/column\:pr-0:is(:where(.group\/column):last-child *) {
    padding-right: calc(var(--spacing) * 0);
  }

  .group-last\/toolbar-group\:hidden\!:is(:where(.group\/toolbar-group):last-child *) {
    display: none !important;
  }

  .group-focus-within\:pointer-events-none:is(:where(.group):focus-within *) {
    pointer-events: none;
  }

  .group-focus-within\:top-0:is(:where(.group):focus-within *) {
    top: calc(var(--spacing) * 0);
  }

  .group-focus-within\:cursor-default:is(:where(.group):focus-within *) {
    cursor: default;
  }

  .group-focus-within\:text-xs:is(:where(.group):focus-within *) {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .group-focus-within\:font-medium:is(:where(.group):focus-within *) {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .group-focus-within\:text-foreground:is(:where(.group):focus-within *) {
    color: var(--foreground);
  }

  @media (hover: hover) {
    .group-hover\:translate-x-0:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/column\:opacity-100:is(:where(.group\/column):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/row\:opacity-100:is(:where(.group\/row):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\/structural\:opacity-100:is(:where(.group\/structural):hover *) {
      opacity: 1;
    }
  }

  .group-has-disabled\:opacity-50:is(:where(.group):has(:disabled) *) {
    opacity: .5;
  }

  .group-has-data-\[resizing\=\"true\"\]\/row\:opacity-0:is(:where(.group\/row):has([data-resizing="true"]) *) {
    opacity: 0;
  }

  .group-has-\[\[data-col\=\"0\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="0"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"0\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="0"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"1\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="1"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"1\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="1"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"10\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="10"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"10\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="10"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"2\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="2"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"2\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="2"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"3\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="3"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"3\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="3"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"4\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="4"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"4\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="4"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"5\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="5"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"5\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="5"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"6\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="6"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"6\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="6"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"7\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="7"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"7\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="7"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"8\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="8"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"8\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="8"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"9\"\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-col="9"]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-col\=\"9\"\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-col="9"][data-resizing="true"]) *) {
    display: block;
  }

  .group-has-\[\[data-resizer-left\]\:hover\]\/table\:block:is(:where(.group\/table):has([data-resizer-left]:hover) *) {
    display: block;
  }

  .group-has-\[\[data-resizer-left\]\[data-resizing\=\"true\"\]\]\/table\:block:is(:where(.group\/table):has([data-resizer-left][data-resizing="true"]) *) {
    display: block;
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[pressed\=true\]\:bg-accent:is(:where(.group)[data-pressed="true"] *) {
    background-color: var(--accent);
  }

  .group-data-\[pressed\=true\]\:text-accent-foreground:is(:where(.group)[data-pressed="true"] *) {
    color: var(--accent-foreground);
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-has-\[\[role\=menuitem\]\]\/menu-group\:block:is(:where(.peer\/menu-group):has([role="menuitem"]) ~ *) {
    display: block;
  }

  .peer-has-\[\[role\=menuitemcheckbox\]\]\/menu-group\:block:is(:where(.peer\/menu-group):has([role="menuitemcheckbox"]) ~ *) {
    display: block;
  }

  .peer-has-\[\[role\=menuitemradio\]\]\/menu-group\:block:is(:where(.peer\/menu-group):has([role="menuitemradio"]) ~ *) {
    display: block;
  }

  .peer-has-\[\[role\=option\]\]\/menu-group\:block:is(:where(.peer\/menu-group):has([role="option"]) ~ *) {
    display: block;
  }

  .selection\:bg-primary ::selection {
    background-color: var(--primary);
  }

  .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:bg-transparent ::selection {
    background-color: #0000;
  }

  .selection\:bg-transparent::selection {
    background-color: #0000;
  }

  .selection\:text-primary-foreground ::selection {
    color: var(--primary-foreground);
  }

  .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-background::file-selector-button {
    background-color: var(--background);
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .placeholder\:text-muted-foreground\/80::placeholder {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .placeholder\:text-muted-foreground\/80::placeholder {
      color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
    }
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:z-10:before {
    content: var(--tw-content);
    z-index: 10;
  }

  .before\:box-border:before {
    content: var(--tw-content);
    box-sizing: border-box;
  }

  .before\:size-full:before {
    content: var(--tw-content);
    width: 100%;
    height: 100%;
  }

  .before\:cursor-text:before {
    content: var(--tw-content);
    cursor: text;
  }

  .before\:border-t:before {
    content: var(--tw-content);
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .before\:border-r:before {
    content: var(--tw-content);
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .before\:border-b:before {
    content: var(--tw-content);
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .before\:border-l:before {
    content: var(--tw-content);
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .before\:border-t-border:before {
    content: var(--tw-content);
    border-top-color: var(--border);
  }

  .before\:border-r-border:before {
    content: var(--tw-content);
    border-right-color: var(--border);
  }

  .before\:border-b-border:before {
    content: var(--tw-content);
    border-bottom-color: var(--border);
  }

  .before\:border-l-border:before {
    content: var(--tw-content);
    border-left-color: var(--border);
  }

  .before\:opacity-30:before {
    content: var(--tw-content);
    opacity: .3;
  }

  .before\:content-\[\'\'\]:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .before\:content-\[attr\(placeholder\)\]:before {
    content: var(--tw-content);
    --tw-content: attr(placeholder);
    content: var(--tw-content);
  }

  .before\:select-none:before {
    content: var(--tw-content);
    -webkit-user-select: none;
    user-select: none;
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:inset-0:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * 0);
  }

  .after\:-top-0\.5:after {
    content: var(--tw-content);
    top: calc(var(--spacing) * -.5);
  }

  .after\:-left-1:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * -1);
  }

  .after\:z-1:after {
    content: var(--tw-content);
    z-index: 1;
  }

  .after\:block:after {
    content: var(--tw-content);
    display: block;
  }

  .after\:flex:after {
    content: var(--tw-content);
    display: flex;
  }

  .after\:h-16:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 16);
  }

  .after\:h-\[calc\(100\%\)\+4px\]:after {
    content: var(--tw-content);
    height: calc(100%) 4px;
  }

  .after\:w-\[3px\]:after {
    content: var(--tw-content);
    width: 3px;
  }

  .after\:w-\[calc\(100\%\+8px\)\]:after {
    content: var(--tw-content);
    width: calc(100% + 8px);
  }

  .after\:rounded-\[6px\]:after {
    content: var(--tw-content);
    border-radius: 6px;
  }

  .after\:rounded-sm:after {
    content: var(--tw-content);
    border-radius: calc(var(--radius)  - 4px);
  }

  .after\:bg-neutral-500\/10:after {
    content: var(--tw-content);
    background-color: #7373731a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .after\:bg-neutral-500\/10:after {
      background-color: color-mix(in oklab, var(--color-neutral-500) 10%, transparent);
    }
  }

  .after\:bg-ring:after {
    content: var(--tw-content);
    background-color: var(--ring);
  }

  .after\:pb-\[var\(--aspect-ratio\)\]:after {
    content: var(--tw-content);
    padding-bottom: var(--aspect-ratio);
  }

  .after\:opacity-0:after {
    content: var(--tw-content);
    opacity: 0;
  }

  .after\:content-\[\"\"\]:after {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .after\:content-\[\'_\'\]:after {
    content: var(--tw-content);
    --tw-content: " ";
    content: var(--tw-content);
  }

  @media (hover: hover) {
    .group-hover\:after\:opacity-100:is(:where(.group):hover *):after {
      content: var(--tw-content);
      opacity: 1;
    }
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  .focus-within\:ring-2:focus-within {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-within\:ring-ring:focus-within {
    --tw-ring-color: var(--ring);
  }

  .focus-within\:ring-offset-2:focus-within {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  @media (hover: hover) {
    .hover\:w-\[106px\]:hover {
      width: 106px;
    }
  }

  @media (hover: hover) {
    .hover\:scale-125:hover {
      --tw-scale-x: 125%;
      --tw-scale-y: 125%;
      --tw-scale-z: 125%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/10:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/10:hover {
        background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-slate-700:hover {
      background-color: var(--color-slate-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: #0000;
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground:hover {
      color: var(--muted-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground\/80:hover {
      color: var(--muted-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:text-muted-foreground\/80:hover {
        color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: var(--primary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:text-white\!:hover {
      color: var(--color-white) !important;
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:bg-primary:focus {
    background-color: var(--primary);
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:text-primary-foreground:focus {
    color: var(--primary-foreground);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-0:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: var(--ring);
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:ring-transparent:focus-visible {
    --tw-ring-color: transparent;
  }

  .focus-visible\:ring-offset-0:focus-visible {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus-visible\:outline-hidden:focus-visible {
      outline: 2px solid #0000;
      outline-offset: 2px;
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-ring:focus-visible {
    outline-color: var(--ring);
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:cursor-grabbing:active {
    cursor: grabbing;
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .has-aria-disabled\:border-input:has([aria-disabled="true"]) {
    border-color: var(--input);
  }

  .has-aria-disabled\:bg-muted:has([aria-disabled="true"]) {
    background-color: var(--muted);
  }

  .has-data-readonly\:w-fit:has([data-readonly]) {
    width: fit-content;
  }

  .has-data-readonly\:cursor-default:has([data-readonly]) {
    cursor: default;
  }

  .has-data-readonly\:border-transparent:has([data-readonly]) {
    border-color: #0000;
  }

  .has-data-readonly\:focus-within\:\[box-shadow\:none\]:has([data-readonly]):focus-within {
    box-shadow: none;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-\[\[data-slate-editor\]\:focus\]\:ring-2:has([data-slate-editor]:focus) {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .has-\[\[role\=menuitem\]\]\:block:has([role="menuitem"]) {
    display: block;
  }

  .has-\[\[role\=menuitemcheckbox\]\]\:block:has([role="menuitemcheckbox"]) {
    display: block;
  }

  .has-\[\[role\=menuitemradio\]\]\:block:has([role="menuitemradio"]) {
    display: block;
  }

  .has-\[\[role\=option\]\]\:block:has([role="option"]) {
    display: block;
  }

  .has-\[button\]\:flex:has(:is(button)) {
    display: flex;
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:pointer-events-none:has( + input:not(:placeholder-shown)) {
    pointer-events: none;
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:top-0:has( + input:not(:placeholder-shown)) {
    top: calc(var(--spacing) * 0);
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:cursor-default:has( + input:not(:placeholder-shown)) {
    cursor: default;
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:text-xs:has( + input:not(:placeholder-shown)) {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:font-medium:has( + input:not(:placeholder-shown)) {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .has-\[\+input\:not\(\:placeholder-shown\)\]\:text-foreground:has( + input:not(:placeholder-shown)) {
    color: var(--foreground);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-checked\:bg-accent[aria-checked="true"] {
    background-color: var(--accent);
  }

  .aria-checked\:text-accent-foreground[aria-checked="true"] {
    color: var(--accent-foreground);
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .aria-selected\:bg-accent[aria-selected="true"] {
    background-color: var(--accent);
  }

  .aria-selected\:bg-accent\/50[aria-selected="true"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-selected\:bg-accent\/50[aria-selected="true"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .aria-selected\:text-accent-foreground[aria-selected="true"] {
    color: var(--accent-foreground);
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: var(--muted-foreground);
  }

  .aria-selected\:opacity-30[aria-selected="true"] {
    opacity: .3;
  }

  .aria-selected\:opacity-100[aria-selected="true"] {
    opacity: 1;
  }

  .data-disabled\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-disabled\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-readonly\:w-fit[data-readonly] {
    width: fit-content;
  }

  :is(.\*\*\:data-slate-placeholder\:top-\[auto_\!important\] *)[data-slate-placeholder] {
    top: auto !important;
  }

  :is(.\*\*\:data-slate-placeholder\:text-muted-foreground\/80 *)[data-slate-placeholder] {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\*\:data-slate-placeholder\:text-muted-foreground\/80 *)[data-slate-placeholder] {
      color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);
    }
  }

  :is(.\*\*\:data-slate-placeholder\:opacity-100\! *)[data-slate-placeholder] {
    opacity: 1 !important;
  }

  .data-\[active-item\=true\]\:bg-accent[data-active-item="true"] {
    background-color: var(--accent);
  }

  .data-\[active-item\=true\]\:text-accent-foreground[data-active-item="true"] {
    color: var(--accent-foreground);
  }

  .data-\[active\=true\]\:bg-muted[data-active="true"] {
    background-color: var(--muted);
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
    pointer-events: none;
  }

  .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[highlighted\=true\]\:bg-accent[data-highlighted="true"] {
    background-color: var(--accent);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
    background-color: var(--accent);
  }

  .data-\[selected\=true\]\:bg-primary\/10[data-selected="true"] {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[selected\=true\]\:bg-primary\/10[data-selected="true"] {
      background-color: color-mix(in oklab, var(--primary) 10%, transparent);
    }
  }

  .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
    color: var(--accent-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=block-selection\]\:left-2 > *)[data-slot="block-selection"] {
    left: calc(var(--spacing) * 2);
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
  }

  .data-\[state\=checked\]\:text-accent-foreground[data-state="checked"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=closed\]\:opacity-0[data-state="closed"] {
    opacity: 0;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
    --tw-exit-translate-x: calc(1 / 2 * 100%);
  }

  .data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
    --tw-exit-translate-y: calc(48% * -1);
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=on\]\:bg-accent[data-state="on"] {
    background-color: var(--accent);
  }

  .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-duration, .15s) var(--tw-ease, ease);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
    --tw-enter-translate-x: calc(1 / 2 * 100%);
  }

  .data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
    --tw-enter-translate-y: calc(48% * -1);
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  @supports (backdrop-blur: var(--tw)) {
    .supports-backdrop-blur\:bg-background\/60 {
      background-color: var(--background);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .supports-backdrop-blur\:bg-background\/60 {
        background-color: color-mix(in oklab, var(--background) 60%, transparent);
      }
    }
  }

  @media (width < 40rem) {
    .max-sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-0 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-2 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-x-4 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (width >= 40rem) {
    .sm\:rounded-lg {
      border-radius: var(--radius);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-24 {
      padding-inline: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-\[max\(64px\,calc\(50\%-350px\)\)\] {
      padding-inline: max(64px, 50% - 350px);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:inline {
      display: inline;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 96rem) {
    .\32 xl\:max-w-\[96rem\] {
      max-width: 96rem;
    }
  }

  .dark\:scale-0:is(.dark *) {
    --tw-scale-x: 0%;
    --tw-scale-y: 0%;
    --tw-scale-z: 0%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:scale-100:is(.dark *) {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .dark\:-rotate-90:is(.dark *) {
    rotate: -90deg;
  }

  .dark\:rotate-0:is(.dark *) {
    rotate: none;
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:bg-purple-900:is(.dark *) {
    background-color: var(--color-purple-900);
  }

  .dark\:bg-red-900:is(.dark *) {
    background-color: var(--color-red-900);
  }

  .dark\:text-muted-foreground:is(.dark *) {
    color: var(--muted-foreground);
  }

  .dark\:text-purple-400:is(.dark *) {
    color: var(--color-purple-400);
  }

  .dark\:text-red-400:is(.dark *) {
    color: var(--color-red-400);
  }

  .dark\:shadow-\[rgba\(255\,_255\,_255\,_0\.1\)_0px_0\.5px_0px_0px_inset\,_rgb\(26\,_29\,_30\)_0px_1px_5px_0px_inset\,_rgb\(76\,_81\,_85\)_0px_0px_0px_0\.5px\,_rgb\(76\,_81\,_85\)_0px_2px_1px_-1px\,_rgb\(76\,_81\,_85\)_0px_1px_0px_0px\]:is(.dark *) {
    --tw-shadow-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:shadow-\[rgba\(255\,_255\,_255\,_0\.1\)_0px_0\.5px_0px_0px_inset\,_rgb\(26\,_29\,_30\)_0px_1px_5px_0px_inset\,_rgb\(76\,_81\,_85\)_0px_0px_0px_0\.5px\,_rgb\(76\,_81\,_85\)_0px_2px_1px_-1px\,_rgb\(76\,_81\,_85\)_0px_1px_0px_0px\]:is(.dark *) {
      --tw-shadow-color: color-mix(in oklab, #ffffff1a 0px .5px 0px 0px inset, #1a1d1e 0px 1px 5px 0px inset, #4c5155 0px 0px 0px .5px, #4c5155 0px 2px 1px -1px, #4c5155 0px 1px 0px 0px var(--tw-shadow-alpha), transparent);
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state="active"] {
    border-color: var(--input);
  }

  .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state="active"] {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state="active"] {
    color: var(--foreground);
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  @media print {
    .print\:hidden {
      display: none;
    }
  }

  @media print {
    .print\:break-inside-avoid {
      break-inside: avoid;
    }
  }

  @media print {
    .print\:placeholder\:text-transparent::placeholder {
      color: #0000;
    }
  }

  .\[\&_\.katex-display\]\:my-0 .katex-display {
    margin-block: calc(var(--spacing) * 0);
  }

  .\[\&_\.react-tweet-theme\]\:my-0 .react-tweet-theme {
    margin-block: calc(var(--spacing) * 0);
  }

  .\[\&_\.react-tweet-theme\]\:ring-2 .react-tweet-theme {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .\[\&_\.react-tweet-theme\]\:ring-ring .react-tweet-theme {
    --tw-ring-color: var(--ring);
  }

  .\[\&_\.react-tweet-theme\]\:ring-offset-2 .react-tweet-theme {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .\[\&_\.slate-selection-area\]\:z-50 .slate-selection-area {
    z-index: 50;
  }

  .\[\&_\.slate-selection-area\]\:border .slate-selection-area {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .\[\&_\>_\.lty-playbtn\]\:absolute > .lty-playbtn {
    position: absolute;
  }

  .\[\&_\>_\.lty-playbtn\]\:top-1\/2 > .lty-playbtn {
    top: 50%;
  }

  .\[\&_\>_\.lty-playbtn\]\:left-1\/2 > .lty-playbtn {
    left: 50%;
  }

  .\[\&_\>_\.lty-playbtn\]\:z-1 > .lty-playbtn {
    z-index: 1;
  }

  .\[\&_\>_\.lty-playbtn\]\:h-\[46px\] > .lty-playbtn {
    height: 46px;
  }

  .\[\&_\>_\.lty-playbtn\]\:w-\[70px\] > .lty-playbtn {
    width: 70px;
  }

  .\[\&_\>_\.lty-playbtn\]\:\[transform\:translate3d\(-50\%\,-50\%\,0\)\] > .lty-playbtn {
    transform: translate3d(-50%, -50%, 0);
  }

  .\[\&_\>_\.lty-playbtn\]\:rounded-\[14\%\] > .lty-playbtn {
    border-radius: 14%;
  }

  .\[\&_\>_\.lty-playbtn\]\:bg-\[\#212121\] > .lty-playbtn {
    background-color: #212121;
  }

  .\[\&_\>_\.lty-playbtn\]\:opacity-80 > .lty-playbtn {
    opacity: .8;
  }

  .\[\&_\>_\.lty-playbtn\]\:\[transition\:all_0\.2s_cubic-bezier\(0\,_0\,_0\.2\,_1\)\] > .lty-playbtn {
    transition: all .2s cubic-bezier(0, 0, .2, 1);
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:absolute > .lty-playbtn:before {
    content: var(--tw-content);
    position: absolute;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:top-1\/2 > .lty-playbtn:before {
    content: var(--tw-content);
    top: 50%;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:left-1\/2 > .lty-playbtn:before {
    content: var(--tw-content);
    left: 50%;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:\[transform\:translate3d\(-50\%\,-50\%\,0\)\] > .lty-playbtn:before {
    content: var(--tw-content);
    transform: translate3d(-50%, -50%, 0);
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:border-y-\[11px\] > .lty-playbtn:before {
    content: var(--tw-content);
    border-block-style: var(--tw-border-style);
    border-block-width: 11px;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:border-r-0 > .lty-playbtn:before {
    content: var(--tw-content);
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:border-l-\[19px\] > .lty-playbtn:before {
    content: var(--tw-content);
    border-left-style: var(--tw-border-style);
    border-left-width: 19px;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:border-\[transparent_transparent_transparent_\#fff\] > .lty-playbtn:before {
    content: var(--tw-content);
    border-color: #0000 #0000 #0000 #fff;
  }

  .\[\&_\>_\.lty-playbtn\]\:before\:content-\[\"\"\] > .lty-playbtn:before {
    content: var(--tw-content);
    --tw-content: "";
    content: var(--tw-content);
  }

  .\[\&_\>_iframe\]\:absolute > iframe {
    position: absolute;
  }

  .\[\&_\>_iframe\]\:top-0 > iframe {
    top: calc(var(--spacing) * 0);
  }

  .\[\&_\>_iframe\]\:left-0 > iframe {
    left: calc(var(--spacing) * 0);
  }

  .\[\&_\>_iframe\]\:size-full > iframe {
    width: 100%;
    height: 100%;
  }

  .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: var(--muted-foreground);
  }

  .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: calc(var(--spacing) * 0);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:size-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: calc(var(--spacing) * 12);
  }

  .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-block: calc(var(--spacing) * 3);
  }

  .\[\&_\[cmdk-item\]_svg\]\:size-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_strong\]\:font-bold strong {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:hidden svg {
    display: none;
  }

  .\[\&_svg\]\:size-4 svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\]\:size-6 svg {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\]\:text-muted-foreground svg {
    color: var(--muted-foreground);
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .\[\&_svg\:not\(\[data-icon\]\)\]\:size-4 svg:not([data-icon]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\.lyt-activated\]\:cursor-\[unset\].lyt-activated {
    cursor: unset;
  }

  .\[\&\.lyt-activated\]\:before\:pointer-events-none.lyt-activated:before {
    content: var(--tw-content);
    pointer-events: none;
  }

  .\[\&\.lyt-activated\]\:before\:absolute.lyt-activated:before {
    content: var(--tw-content);
    position: absolute;
  }

  .\[\&\.lyt-activated\]\:before\:top-0.lyt-activated:before {
    content: var(--tw-content);
    top: calc(var(--spacing) * 0);
  }

  .\[\&\.lyt-activated\]\:before\:h-\[60px\].lyt-activated:before {
    content: var(--tw-content);
    height: 60px;
  }

  .\[\&\.lyt-activated\]\:before\:w-full.lyt-activated:before {
    content: var(--tw-content);
    width: 100%;
  }

  .\[\&\.lyt-activated\]\:before\:bg-\[url\(data\:image\/png\;base64\,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT\+OqFAAAAdklEQVQoz42QQQ7AIAgEF\/T\/D\+kbq\/RWAlnQyyazA4aoAB4FsBSA\/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg\=\=\)\].lyt-activated:before {
    content: var(--tw-content);
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==");
  }

  .\[\&\.lyt-activated\]\:before\:bg-top.lyt-activated:before {
    content: var(--tw-content);
    background-position: top;
  }

  .\[\&\.lyt-activated\]\:before\:bg-repeat-x.lyt-activated:before {
    content: var(--tw-content);
    background-repeat: repeat-x;
  }

  .\[\&\.lyt-activated\]\:before\:pb-\[50px\].lyt-activated:before {
    content: var(--tw-content);
    padding-bottom: 50px;
  }

  .\[\&\.lyt-activated\]\:before\:opacity-0.lyt-activated:before {
    content: var(--tw-content);
    opacity: 0;
  }

  .\[\&\.lyt-activated\]\:before\:\[transition\:all_0\.2s_cubic-bezier\(0\,_0\,_0\.2\,_1\)\].lyt-activated:before {
    content: var(--tw-content);
    transition: all .2s cubic-bezier(0, 0, .2, 1);
  }

  .\[\&\.lyt-activated_\>_\.lty-playbtn\]\:pointer-events-none.lyt-activated > .lty-playbtn {
    pointer-events: none;
  }

  .\[\&\.lyt-activated_\>_\.lty-playbtn\]\:opacity-0\!.lyt-activated > .lty-playbtn {
    opacity: 0 !important;
  }

  .\[\&\:\:-webkit-scrollbar\]\:w-4::-webkit-scrollbar {
    width: calc(var(--spacing) * 4);
  }

  .\[\&\:\:-webkit-scrollbar-button\]\:hidden::-webkit-scrollbar-button {
    display: none;
  }

  .\[\&\:\:-webkit-scrollbar-button\]\:size-0::-webkit-scrollbar-button {
    width: calc(var(--spacing) * 0);
    height: calc(var(--spacing) * 0);
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:min-h-11::-webkit-scrollbar-thumb {
    min-height: calc(var(--spacing) * 11);
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:rounded-full::-webkit-scrollbar-thumb {
    border-radius: 3.40282e38px;
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:border-4::-webkit-scrollbar-thumb {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:border-solid::-webkit-scrollbar-thumb {
    --tw-border-style: solid;
    border-style: solid;
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:border-popover::-webkit-scrollbar-thumb {
    border-color: var(--popover);
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:bg-muted::-webkit-scrollbar-thumb {
    background-color: var(--muted);
  }

  .\[\&\:\:-webkit-scrollbar-thumb\]\:bg-clip-padding::-webkit-scrollbar-thumb {
    background-clip: padding-box;
  }

  @media (hover: hover) {
    .\[\&\:\:-webkit-scrollbar-thumb\]\:hover\:bg-muted-foreground\/25::-webkit-scrollbar-thumb:hover {
      background-color: var(--muted-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .\[\&\:\:-webkit-scrollbar-thumb\]\:hover\:bg-muted-foreground\/25::-webkit-scrollbar-thumb:hover {
        background-color: color-mix(in oklab, var(--muted-foreground) 25%, transparent);
      }
    }
  }

  .focus\:\[\&\:\:placeholder\]\:opacity-0:focus::placeholder {
    opacity: 0;
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: var(--accent);
  }

  .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside) {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has([aria-selected].day-outside) {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\&\:has\(\[role\=option\]\)\]\:block:has([role="option"]) {
    display: block;
  }

  .\[\&\:hover_\>_\.lty-playbtn\]\:bg-\[red\]:hover > .lty-playbtn {
    background-color: red;
  }

  .\[\&\:hover_\>_\.lty-playbtn\]\:opacity-100:hover > .lty-playbtn {
    opacity: 1;
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\*\:\[\.hljs-addition\]\:bg-\[\#f0fff4\] *).hljs-addition {
    background-color: #f0fff4;
  }

  :is(.\*\*\:\[\.hljs-addition\]\:text-\[\#22863a\] *).hljs-addition {
    color: #22863a;
  }

  :is(.\*\*\:\[\.hljs-attr\,\.hljs-attribute\,\.hljs-literal\,\.hljs-meta\,\.hljs-number\,\.hljs-operator\,\.hljs-selector-attr\,\.hljs-selector-class\,\.hljs-selector-id\,\.hljs-variable\]\:text-\[\#005cc5\] *):is(.hljs-attr, .hljs-attribute, .hljs-literal, .hljs-meta, .hljs-number, .hljs-operator, .hljs-selector-attr, .hljs-selector-class, .hljs-selector-id, .hljs-variable) {
    color: #005cc5;
  }

  :is(.\*\*\:\[\.hljs-built_in\,\.hljs-symbol\]\:text-\[\#e36209\] *):is(.hljs-built in, .hljs-symbol) {
    color: #e36209;
  }

  :is(.\*\*\:\[\.hljs-bullet\]\:text-\[\#735c0f\] *).hljs-bullet {
    color: #735c0f;
  }

  :is(.\*\*\:\[\.hljs-comment\,\.hljs-code\,\.hljs-formula\]\:text-\[\#6a737d\] *):is(.hljs-comment, .hljs-code, .hljs-formula) {
    color: #6a737d;
  }

  :is(.\*\*\:\[\.hljs-deletion\]\:bg-\[\#ffeef0\] *).hljs-deletion {
    background-color: #ffeef0;
  }

  :is(.\*\*\:\[\.hljs-deletion\]\:text-\[\#b31d28\] *).hljs-deletion {
    color: #b31d28;
  }

  :is(.\*\*\:\[\.hljs-emphasis\]\:italic *).hljs-emphasis {
    font-style: italic;
  }

  :is(.\*\*\:\[\.hljs-keyword\,\.hljs-doctag\,\.hljs-template-tag\,\.hljs-template-variable\,\.hljs-type\,\.hljs-variable\.language_\]\:text-\[\#d73a49\] *):is(.hljs-keyword, .hljs-doctag, .hljs-template-tag, .hljs-template-variable, .hljs-type, .hljs-variable.language) {
    color: #d73a49;
  }

  :is(.\*\*\:\[\.hljs-name\,\.hljs-quote\,\.hljs-selector-tag\,\.hljs-selector-pseudo\]\:text-\[\#22863a\] *):is(.hljs-name, .hljs-quote, .hljs-selector-tag, .hljs-selector-pseudo) {
    color: #22863a;
  }

  :is(.\*\*\:\[\.hljs-regexp\,\.hljs-string\,\.hljs-meta_\.hljs-string\]\:text-\[\#032f62\] *):is(.hljs-regexp, .hljs-string, .hljs-meta .hljs-string) {
    color: #032f62;
  }

  :is(.\*\*\:\[\.hljs-section\]\:font-bold *).hljs-section {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  :is(.\*\*\:\[\.hljs-section\]\:text-\[\#005cc5\] *).hljs-section {
    color: #005cc5;
  }

  :is(.\*\*\:\[\.hljs-strong\]\:font-bold *).hljs-strong {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  :is(.\*\*\:\[\.hljs-title\,\.hljs-title\.class_\,\.hljs-title\.class_\.inherited__\,\.hljs-title\.function_\]\:text-\[\#6f42c1\] *):is(.hljs-title, .hljs-title.class, .hljs-title.class .inherited, .hljs-title.function) {
    color: #6f42c1;
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }
}

:root {
  --radius: .625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.145 0 0);
  --primary: oklch(.205 0 0);
  --primary-foreground: oklch(.985 0 0);
  --secondary: oklch(.97 0 0);
  --secondary-foreground: oklch(.205 0 0);
  --muted: oklch(.97 0 0);
  --muted-foreground: oklch(.556 0 0);
  --accent: oklch(.97 0 0);
  --accent-foreground: oklch(.205 0 0);
  --destructive: oklch(.577 .245 27.325);
  --border: oklch(.922 0 0);
  --input: oklch(.922 0 0);
  --ring: oklch(.708 0 0);
  --chart-1: oklch(.646 .222 41.116);
  --chart-2: oklch(.6 .118 184.704);
  --chart-3: oklch(.398 .07 227.392);
  --chart-4: oklch(.828 .189 84.429);
  --chart-5: oklch(.769 .188 70.08);
  --sidebar: oklch(.985 0 0);
  --sidebar-foreground: oklch(.145 0 0);
  --sidebar-primary: oklch(.205 0 0);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.97 0 0);
  --sidebar-accent-foreground: oklch(.205 0 0);
  --sidebar-border: oklch(.922 0 0);
  --sidebar-ring: oklch(.708 0 0);
}

.dark {
  --background: oklch(.145 0 0);
  --foreground: oklch(.985 0 0);
  --card: oklch(.205 0 0);
  --card-foreground: oklch(.985 0 0);
  --popover: oklch(.205 0 0);
  --popover-foreground: oklch(.985 0 0);
  --primary: oklch(.922 0 0);
  --primary-foreground: oklch(.205 0 0);
  --secondary: oklch(.269 0 0);
  --secondary-foreground: oklch(.985 0 0);
  --muted: oklch(.269 0 0);
  --muted-foreground: oklch(.708 0 0);
  --accent: oklch(.269 0 0);
  --accent-foreground: oklch(.985 0 0);
  --destructive: oklch(.704 .191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(.556 0 0);
  --chart-1: oklch(.488 .243 264.376);
  --chart-2: oklch(.696 .17 162.48);
  --chart-3: oklch(.769 .188 70.08);
  --chart-4: oklch(.627 .265 303.9);
  --chart-5: oklch(.645 .246 16.439);
  --sidebar: oklch(.205 0 0);
  --sidebar-foreground: oklch(.985 0 0);
  --sidebar-primary: oklch(.488 .243 264.376);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.269 0 0);
  --sidebar-accent-foreground: oklch(.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(.556 0 0);
}

@layer base {
  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}


/* [project]/src/styles/content-styles.css [app-client] (css) */
.content-html h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: .75rem 0;
  color: #050505;
}

.content-html h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: .75rem 0;
  color: #050505;
}

.content-html h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: .75rem 0;
  color: #050505;
}

.content-html blockquote {
  border-left: 4px solid #ccc;
  margin: 1rem 0;
  padding-left: 1rem;
  color: #65676b;
  font-style: italic;
}

.content-html ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

.content-html ol {
  list-style-type: decimal;
  margin: 1rem 0;
  padding-left: 2rem;
}

.content-html li {
  margin: .25rem 0;
}

.content-html a {
  color: #216fdb;
  text-decoration: underline;
  cursor: pointer;
}

.content-html p {
  margin: .75rem 0;
}

.content-html strong, .content-html b {
  font-weight: bold;
}

.content-html em, .content-html i {
  font-style: italic;
}

.content-html u {
  text-decoration: underline;
}

.content-html s, .content-html strike {
  text-decoration: line-through;
}

.content-html img {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
}


/*# sourceMappingURL=%5Broot%20of%20the%20server%5D__0dc8ee60._.css.map*/
