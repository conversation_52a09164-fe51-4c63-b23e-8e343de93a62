{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwYGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwSGFWNOITddY4.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geist/v3/gyByhwUxId8gMEwcGFWNOITd.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Fallback';\n    src: local(\"Arial\");\n    ascent-override: 95.94%;\ndescent-override: 28.16%;\nline-gap-override: 0.00%;\nsize-adjust: 104.76%;\n\n}\n.className {\n    font-family: 'Geist', 'Geist Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-sans: 'Geist', 'Geist Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrMdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrkdmhHkjkotbA.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Geist Mono';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/geistmono/v3/or3nQ6H-1_WfwkMZI_qYFrcdmhHkjko.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Geist Mono Fallback';\n    src: local(\"Arial\");\n    ascent-override: 74.67%;\ndescent-override: 21.92%;\nline-gap-override: 0.00%;\nsize-adjust: 134.59%;\n\n}\n.className {\n    font-family: 'Geist Mono', 'Geist Mono Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-geist-mono: 'Geist Mono', 'Geist Mono Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.4 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-sans: var(--font-geist-sans);\n    --font-mono: var(--font-geist-mono);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-100: oklch(94.6% 0.033 307.174);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-800: oklch(43.8% 0.218 303.724);\n    --color-purple-900: oklch(38.1% 0.176 304.987);\n    --color-slate-50: oklch(98.4% 0.003 247.858);\n    --color-slate-200: oklch(92.9% 0.013 255.508);\n    --color-slate-300: oklch(86.9% 0.022 252.894);\n    --color-slate-500: oklch(55.4% 0.046 257.417);\n    --color-slate-600: oklch(44.6% 0.043 257.281);\n    --color-slate-700: oklch(37.2% 0.044 257.287);\n    --color-slate-800: oklch(27.9% 0.041 260.031);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-neutral-500: oklch(55.6% 0 0);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tight: -0.025em;\n    --tracking-widest: 0.1em;\n    --radius-xs: 0.125rem;\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --blur-xs: 4px;\n    --blur-sm: 8px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .pointer-events-auto {\n    pointer-events: auto;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .start-1 {\n    inset-inline-start: calc(var(--spacing) * 1);\n  }\n  .-top-2 {\n    top: calc(var(--spacing) * -2);\n  }\n  .-top-px {\n    top: -1px;\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1 {\n    top: calc(var(--spacing) * 1);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-\\[5px\\] {\n    top: 5px;\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-1 {\n    right: calc(var(--spacing) * -1);\n  }\n  .-right-3 {\n    right: calc(var(--spacing) * -3);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-0\\.5 {\n    right: calc(var(--spacing) * 0.5);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-\\[-1\\.5px\\] {\n    right: -1.5px;\n  }\n  .right-\\[-11px\\] {\n    right: -11px;\n  }\n  .right-\\[28px\\] {\n    right: 28px;\n  }\n  .-bottom-1 {\n    bottom: calc(var(--spacing) * -1);\n  }\n  .-bottom-1\\.5 {\n    bottom: calc(var(--spacing) * -1.5);\n  }\n  .-bottom-px {\n    bottom: -1px;\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-1 {\n    bottom: calc(var(--spacing) * 1);\n  }\n  .bottom-2 {\n    bottom: calc(var(--spacing) * 2);\n  }\n  .bottom-4 {\n    bottom: calc(var(--spacing) * 4);\n  }\n  .-left-0\\.5 {\n    left: calc(var(--spacing) * -0.5);\n  }\n  .-left-1 {\n    left: calc(var(--spacing) * -1);\n  }\n  .-left-3 {\n    left: calc(var(--spacing) * -3);\n  }\n  .-left-6 {\n    left: calc(var(--spacing) * -6);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1 {\n    left: calc(var(--spacing) * 1);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-2\\.5 {\n    left: calc(var(--spacing) * 2.5);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-\\[-1\\.5px\\] {\n    left: -1.5px;\n  }\n  .left-\\[-10\\.5px\\] {\n    left: -10.5px;\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .z-1 {\n    z-index: 1;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-30 {\n    z-index: 30;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-51 {\n    z-index: 51;\n  }\n  .z-100 {\n    z-index: 100;\n  }\n  .z-500 {\n    z-index: 500;\n  }\n  .z-\\[9999\\] {\n    z-index: 9999;\n  }\n  .col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .m-0 {\n    margin: calc(var(--spacing) * 0);\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-1 {\n    margin-inline: calc(var(--spacing) * 1);\n  }\n  .mx-1\\.5 {\n    margin-inline: calc(var(--spacing) * 1.5);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .mx-px {\n    margin-inline: 1px;\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-1\\.5 {\n    margin-block: calc(var(--spacing) * 1.5);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .my-px {\n    margin-block: 1px;\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-1\\.5 {\n    margin-top: calc(var(--spacing) * 1.5);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-\\[0\\.75em\\] {\n    margin-top: 0.75em;\n  }\n  .mt-\\[1\\.4em\\] {\n    margin-top: 1.4em;\n  }\n  .mt-\\[1\\.6em\\] {\n    margin-top: 1.6em;\n  }\n  .mt-\\[1em\\] {\n    margin-top: 1em;\n  }\n  .-mr-3 {\n    margin-right: calc(var(--spacing) * -3);\n  }\n  .mr-0 {\n    margin-right: calc(var(--spacing) * 0);\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-1\\.5 {\n    margin-right: calc(var(--spacing) * 1.5);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-auto {\n    margin-right: auto;\n  }\n  .mb-0 {\n    margin-bottom: calc(var(--spacing) * 0);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-2\\.5 {\n    margin-bottom: calc(var(--spacing) * 2.5);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .-ml-2 {\n    margin-left: calc(var(--spacing) * -2);\n  }\n  .-ml-3 {\n    margin-left: calc(var(--spacing) * -3);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-1\\.5 {\n    margin-left: calc(var(--spacing) * 1.5);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .ml-px {\n    margin-left: 1px;\n  }\n  .box-border {\n    box-sizing: border-box;\n  }\n  .box-content {\n    box-sizing: content-box;\n  }\n  .block {\n    display: block;\n  }\n  .contents {\n    display: contents;\n  }\n  .flex {\n    display: flex;\n  }\n  .flex\\! {\n    display: flex !important;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .\\!size-3 {\n    width: calc(var(--spacing) * 3) !important;\n    height: calc(var(--spacing) * 3) !important;\n  }\n  .\\!size-3\\.5 {\n    width: calc(var(--spacing) * 3.5) !important;\n    height: calc(var(--spacing) * 3.5) !important;\n  }\n  .size-0 {\n    width: calc(var(--spacing) * 0);\n    height: calc(var(--spacing) * 0);\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-3 {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n  .size-3\\! {\n    width: calc(var(--spacing) * 3) !important;\n    height: calc(var(--spacing) * 3) !important;\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-5 {\n    width: calc(var(--spacing) * 5);\n    height: calc(var(--spacing) * 5);\n  }\n  .size-6 {\n    width: calc(var(--spacing) * 6);\n    height: calc(var(--spacing) * 6);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-10 {\n    width: calc(var(--spacing) * 10);\n    height: calc(var(--spacing) * 10);\n  }\n  .size-\\[28px\\] {\n    width: 28px;\n    height: 28px;\n  }\n  .size-\\[130px\\] {\n    width: 130px;\n    height: 130px;\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-11 {\n    height: calc(var(--spacing) * 11);\n  }\n  .h-14 {\n    height: calc(var(--spacing) * 14);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-\\[1\\.2rem\\] {\n    height: 1.2rem;\n  }\n  .h-\\[1\\.3em\\] {\n    height: 1.3em;\n  }\n  .h-\\[1\\.5em\\] {\n    height: 1.5em;\n  }\n  .h-\\[19px\\] {\n    height: 19px;\n  }\n  .h-\\[23rem\\] {\n    height: 23rem;\n  }\n  .h-\\[24px\\] {\n    height: 24px;\n  }\n  .h-\\[28px\\] {\n    height: 28px;\n  }\n  .h-\\[344px\\] {\n    height: 344px;\n  }\n  .h-\\[600px\\] {\n    height: 600px;\n  }\n  .h-\\[650px\\] {\n    height: 650px;\n  }\n  .h-\\[calc\\(100\\%-1px\\)\\] {\n    height: calc(100% - 1px);\n  }\n  .h-\\[calc\\(100\\%_\\+_8px\\)\\] {\n    height: calc(100% + 8px);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-fit {\n    height: fit-content;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-14 {\n    max-height: calc(var(--spacing) * 14);\n  }\n  .max-h-\\[50vh\\] {\n    max-height: 50vh;\n  }\n  .max-h-\\[288px\\] {\n    max-height: 288px;\n  }\n  .max-h-\\[300px\\] {\n    max-height: 300px;\n  }\n  .max-h-\\[500px\\] {\n    max-height: 500px;\n  }\n  .max-h-\\[calc\\(100vh-4rem\\)\\] {\n    max-height: calc(100vh - 4rem);\n  }\n  .max-h-\\[min\\(50dvh\\,calc\\(-24px\\+var\\(--radix-popper-available-height\\)\\)\\)\\] {\n    max-height: min(50dvh, calc(-24px + var(--radix-popper-available-height)));\n  }\n  .max-h-\\[min\\(70vh\\,320px\\)\\] {\n    max-height: min(70vh, 320px);\n  }\n  .max-h-screen {\n    max-height: 100vh;\n  }\n  .min-h-14 {\n    min-height: calc(var(--spacing) * 14);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-\\[1lh\\] {\n    min-height: 1lh;\n  }\n  .min-h-\\[25px\\] {\n    min-height: 25px;\n  }\n  .min-h-\\[50\\%\\] {\n    min-height: 50%;\n  }\n  .min-h-\\[100px\\] {\n    min-height: 100px;\n  }\n  .min-h-\\[400px\\] {\n    min-height: 400px;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .w-0\\.5 {\n    width: calc(var(--spacing) * 0.5);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-4\\.5 {\n    width: calc(var(--spacing) * 4.5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-9 {\n    width: calc(var(--spacing) * 9);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-\\[1\\.2rem\\] {\n    width: 1.2rem;\n  }\n  .w-\\[180px\\] {\n    width: 180px;\n  }\n  .w-\\[200px\\] {\n    width: 200px;\n  }\n  .w-\\[300px\\] {\n    width: 300px;\n  }\n  .w-\\[330px\\] {\n    width: 330px;\n  }\n  .w-\\[380px\\] {\n    width: 380px;\n  }\n  .w-\\[min\\(100\\%\\,600px\\)\\] {\n    width: min(100%, 600px);\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .w-screen {\n    width: 100vw;\n  }\n  .max-w-\\[80vw\\] {\n    max-width: 80vw;\n  }\n  .max-w-\\[700px\\] {\n    max-width: 700px;\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-\\[calc\\(100vw-24px\\)\\] {\n    max-width: calc(100vw - 24px);\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-32 {\n    min-width: calc(var(--spacing) * 32);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[92px\\] {\n    min-width: 92px;\n  }\n  .min-w-\\[125px\\] {\n    min-width: 125px;\n  }\n  .min-w-\\[130px\\] {\n    min-width: 130px;\n  }\n  .min-w-\\[180px\\] {\n    min-width: 180px;\n  }\n  .min-w-\\[220px\\] {\n    min-width: 220px;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .min-w-full {\n    min-width: 100%;\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .table-fixed {\n    table-layout: fixed;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .border-collapse {\n    border-collapse: collapse;\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-2 {\n    --tw-translate-x: calc(var(--spacing) * -2);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-full {\n    --tw-translate-x: -100%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-0 {\n    --tw-scale-x: 0%;\n    --tw-scale-y: 0%;\n    --tw-scale-z: 0%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-0 {\n    rotate: 0deg;\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-in {\n    animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-col-resize {\n    cursor: col-resize;\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-grab {\n    cursor: grab;\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .cursor-row-resize {\n    cursor: row-resize;\n  }\n  .cursor-text {\n    cursor: text;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .appearance-none {\n    appearance: none;\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-8 {\n    grid-template-columns: repeat(8, minmax(0, 1fr));\n  }\n  .grid-cols-\\[repeat\\(10\\,1fr\\)\\] {\n    grid-template-columns: repeat(10,1fr);\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .place-items-center {\n    place-items: center;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-evenly {\n    justify-content: space-evenly;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-0 {\n    gap: calc(var(--spacing) * 0);\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-10 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 10) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 10) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-visible {\n    overflow: visible;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-\\[50\\%\\] {\n    border-radius: 50%;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-t-lg {\n    border-top-left-radius: var(--radius);\n    border-top-right-radius: var(--radius);\n  }\n  .rounded-r-md {\n    border-top-right-radius: calc(var(--radius) - 2px);\n    border-bottom-right-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-r-none {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-b-2 {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 2px;\n  }\n  .border-l-0 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 0px;\n  }\n  .border-l-2 {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 2px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-none {\n    --tw-border-style: none;\n    border-style: none;\n  }\n  .border-solid {\n    --tw-border-style: solid;\n    border-style: solid;\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-current {\n    border-color: currentcolor;\n  }\n  .border-gray-300 {\n    border-color: var(--color-gray-300);\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-muted {\n    border-color: var(--muted);\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-slate-200 {\n    border-color: var(--color-slate-200);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-b-border {\n    border-bottom-color: var(--border);\n  }\n  .border-b-gray-300 {\n    border-bottom-color: var(--color-gray-300);\n  }\n  .border-b-gray-500 {\n    border-bottom-color: var(--color-gray-500);\n  }\n  .border-b-purple-100 {\n    border-bottom-color: var(--color-purple-100);\n  }\n  .bg-\\(--cellBackground\\) {\n    background-color: var(--cellBackground);\n  }\n  .bg-\\[rgba\\(0\\,0\\,0\\,0\\.5\\)\\] {\n    background-color: rgba(0,0,0,0.5);\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-background\\/95 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 95%, transparent);\n    }\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-black\\/80 {\n    background-color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-current {\n    background-color: currentcolor;\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-gray-300\\/25 {\n    background-color: color-mix(in srgb, oklch(87.2% 0.01 258.338) 25%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-300) 25%, transparent);\n    }\n  }\n  .bg-gray-400\\/25 {\n    background-color: color-mix(in srgb, oklch(70.7% 0.022 261.325) 25%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-400) 25%, transparent);\n    }\n  }\n  .bg-inherit {\n    background-color: inherit;\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-muted\\/60 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 60%, transparent);\n    }\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-popover\\/90 {\n    background-color: var(--popover);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--popover) 90%, transparent);\n    }\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-primary\\/40 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 40%, transparent);\n    }\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-purple-100 {\n    background-color: var(--color-purple-100);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-ring {\n    background-color: var(--ring);\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-slate-50 {\n    background-color: var(--color-slate-50);\n  }\n  .bg-slate-700 {\n    background-color: var(--color-slate-700);\n  }\n  .bg-slate-800 {\n    background-color: var(--color-slate-800);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-cover {\n    background-size: cover;\n  }\n  .bg-clip-content {\n    background-clip: content-box;\n  }\n  .bg-center {\n    background-position: center;\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .stroke-\\[3px\\] {\n    stroke-width: 3px;\n  }\n  .object-contain {\n    object-fit: contain;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-1\\.5 {\n    padding: calc(var(--spacing) * 1.5);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-\\[3px\\] {\n    padding: 3px;\n  }\n  .p-px {\n    padding: 1px;\n  }\n  .px-0 {\n    padding-inline: calc(var(--spacing) * 0);\n  }\n  .px-0\\.5 {\n    padding-inline: calc(var(--spacing) * 0.5);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-5 {\n    padding-inline: calc(var(--spacing) * 5);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-10 {\n    padding-inline: calc(var(--spacing) * 10);\n  }\n  .px-16 {\n    padding-inline: calc(var(--spacing) * 16);\n  }\n  .px-\\[0\\.3em\\] {\n    padding-inline: 0.3em;\n  }\n  .px-px {\n    padding-inline: 1px;\n  }\n  .py-0 {\n    padding-block: calc(var(--spacing) * 0);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-5 {\n    padding-block: calc(var(--spacing) * 5);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-10 {\n    padding-block: calc(var(--spacing) * 10);\n  }\n  .py-\\[0\\.2em\\] {\n    padding-block: 0.2em;\n  }\n  .py-\\[1\\.5px\\] {\n    padding-block: 1.5px;\n  }\n  .py-\\[3px\\] {\n    padding-block: 3px;\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-0\\.5 {\n    padding-top: calc(var(--spacing) * 0.5);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-1\\.5 {\n    padding-top: calc(var(--spacing) * 1.5);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-\\[2px\\] {\n    padding-top: 2px;\n  }\n  .pt-\\[3px\\] {\n    padding-top: 3px;\n  }\n  .pr-1 {\n    padding-right: calc(var(--spacing) * 1);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-3 {\n    padding-right: calc(var(--spacing) * 3);\n  }\n  .pr-4 {\n    padding-right: calc(var(--spacing) * 4);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-9 {\n    padding-right: calc(var(--spacing) * 9);\n  }\n  .pr-10 {\n    padding-right: calc(var(--spacing) * 10);\n  }\n  .pb-0 {\n    padding-bottom: calc(var(--spacing) * 0);\n  }\n  .pb-1 {\n    padding-bottom: calc(var(--spacing) * 1);\n  }\n  .pb-2 {\n    padding-bottom: calc(var(--spacing) * 2);\n  }\n  .pb-72 {\n    padding-bottom: calc(var(--spacing) * 72);\n  }\n  .pb-\\[51\\.25\\%\\] {\n    padding-bottom: 51.25%;\n  }\n  .pb-\\[56\\.25\\%\\] {\n    padding-bottom: 56.25%;\n  }\n  .pb-\\[56\\.0417\\%\\] {\n    padding-bottom: 56.0417%;\n  }\n  .pb-\\[75\\%\\] {\n    padding-bottom: 75%;\n  }\n  .pb-px {\n    padding-bottom: 1px;\n  }\n  .pl-0\\.5 {\n    padding-left: calc(var(--spacing) * 0.5);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-3 {\n    padding-left: calc(var(--spacing) * 3);\n  }\n  .pl-6 {\n    padding-left: calc(var(--spacing) * 6);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-\\[26px\\] {\n    padding-left: 26px;\n  }\n  .pl-\\[32px\\] {\n    padding-left: 32px;\n  }\n  .pl-\\[50px\\] {\n    padding-left: 50px;\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-justify {\n    text-align: justify;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .align-baseline {\n    vertical-align: baseline;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .align-text-bottom {\n    vertical-align: text-bottom;\n  }\n  .font-\\[inherit\\] {\n    font-family: inherit;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[0\\.8rem\\] {\n    font-size: 0.8rem;\n  }\n  .text-\\[1\\.1em\\] {\n    font-size: 1.1em;\n  }\n  .text-\\[1\\.5em\\] {\n    font-size: 1.5em;\n  }\n  .text-\\[1\\.25em\\] {\n    font-size: 1.25em;\n  }\n  .text-\\[1\\.875em\\] {\n    font-size: 1.875em;\n  }\n  .leading-\\[normal\\] {\n    --tw-leading: normal;\n    line-height: normal;\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-nowrap {\n    text-wrap: nowrap;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .whitespace-pre-wrap {\n    white-space: pre-wrap;\n  }\n  .text-accent-foreground {\n    color: var(--accent-foreground);\n  }\n  .text-background {\n    color: var(--background);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-destructive {\n    color: var(--destructive);\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-inherit {\n    color: inherit;\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-muted-foreground\\/70 {\n    color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);\n    }\n  }\n  .text-muted-foreground\\/80 {\n    color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);\n    }\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-purple-800 {\n    color: var(--color-purple-800);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-slate-300 {\n    color: var(--color-slate-300);\n  }\n  .text-slate-500 {\n    color: var(--color-slate-500);\n  }\n  .text-slate-600 {\n    color: var(--color-slate-600);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .line-through {\n    text-decoration-line: line-through;\n  }\n  .no-underline {\n    text-decoration-line: none;\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .decoration-primary {\n    text-decoration-color: var(--primary);\n  }\n  .decoration-\\[0\\.5px\\] {\n    text-decoration-thickness: 0.5px;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .caret-primary {\n    caret-color: var(--primary);\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-2 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[rgba\\(255\\,_255\\,_255\\,_0\\.1\\)_0px_0\\.5px_0px_0px_inset\\,_rgb\\(248\\,_249\\,_250\\)_0px_1px_5px_0px_inset\\,_rgb\\(193\\,_200\\,_205\\)_0px_0px_0px_0\\.5px\\,_rgb\\(193\\,_200\\,_205\\)_0px_2px_1px_-1px\\,_rgb\\(193\\,_200\\,_205\\)_0px_1px_0px_0px\\] {\n    --tw-shadow-color: rgba(255, 255, 255, 0.1);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, rgba(255, 255, 255, 0.1) 0px 0.5px 0px 0px inset, rgb(248, 249, 250) 0px 1px 5px 0px inset, rgb(193, 200, 205) 0px 0px 0px 0.5px, rgb(193, 200, 205) 0px 2px 1px -1px, rgb(193, 200, 205) 0px 1px 0px 0px var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .ring-ring {\n    --tw-ring-color: var(--ring);\n  }\n  .ring-offset-2 {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .\\!filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,) !important;\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xs {\n    --tw-backdrop-blur: blur(var(--blur-xs));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-75 {\n    --tw-duration: 75ms;\n    transition-duration: 75ms;\n  }\n  .duration-100 {\n    --tw-duration: 100ms;\n    transition-duration: 100ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .\\[contain\\:content\\] {\n    contain: content;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .select-text {\n    -webkit-user-select: text;\n    user-select: text;\n  }\n  .\\[tab-size\\:2\\] {\n    tab-size: 2;\n  }\n  .fade-in {\n    --tw-enter-opacity: 0;\n  }\n  .fade-in-80 {\n    --tw-enter-opacity: .8;\n  }\n  .running {\n    animation-play-state: running;\n  }\n  .\\*\\:m-0 {\n    :is(& > *) {\n      margin: calc(var(--spacing) * 0);\n    }\n  }\n  .not-last\\:border-b {\n    &:not(*:last-child) {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .group-first\\/column\\:-left-1 {\n    &:is(:where(.group\\/column):first-child *) {\n      left: calc(var(--spacing) * -1);\n    }\n  }\n  .group-first\\/column\\:pl-0 {\n    &:is(:where(.group\\/column):first-child *) {\n      padding-left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-last\\/column\\:-right-1 {\n    &:is(:where(.group\\/column):last-child *) {\n      right: calc(var(--spacing) * -1);\n    }\n  }\n  .group-last\\/column\\:pr-0 {\n    &:is(:where(.group\\/column):last-child *) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .group-last\\/toolbar-group\\:hidden\\! {\n    &:is(:where(.group\\/toolbar-group):last-child *) {\n      display: none !important;\n    }\n  }\n  .group-focus-within\\:pointer-events-none {\n    &:is(:where(.group):focus-within *) {\n      pointer-events: none;\n    }\n  }\n  .group-focus-within\\:top-0 {\n    &:is(:where(.group):focus-within *) {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .group-focus-within\\:cursor-default {\n    &:is(:where(.group):focus-within *) {\n      cursor: default;\n    }\n  }\n  .group-focus-within\\:text-xs {\n    &:is(:where(.group):focus-within *) {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .group-focus-within\\:font-medium {\n    &:is(:where(.group):focus-within *) {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .group-focus-within\\:text-foreground {\n    &:is(:where(.group):focus-within *) {\n      color: var(--foreground);\n    }\n  }\n  .group-hover\\:translate-x-0 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-translate-x: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\/column\\:opacity-100 {\n    &:is(:where(.group\\/column):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\/row\\:opacity-100 {\n    &:is(:where(.group\\/row):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\/structural\\:opacity-100 {\n    &:is(:where(.group\\/structural):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-has-disabled\\:opacity-50 {\n    &:is(:where(.group):has(*:disabled) *) {\n      opacity: 50%;\n    }\n  }\n  .group-has-data-\\[resizing\\=\\\"true\\\"\\]\\/row\\:opacity-0 {\n    &:is(:where(.group\\/row):has(*[data-resizing=\"true\"]) *) {\n      opacity: 0%;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"0\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"0\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"0\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"0\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"1\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"1\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"1\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"1\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"10\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"10\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"10\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"10\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"2\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"2\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"2\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"2\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"3\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"3\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"3\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"3\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"4\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"4\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"4\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"4\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"5\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"5\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"5\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"5\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"6\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"6\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"6\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"6\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"7\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"7\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"7\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"7\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"8\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"8\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"8\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"8\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"9\\\"\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"9\"]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-col\\=\\\"9\\\"\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-col=\"9\"][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-resizer-left\\]\\:hover\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-resizer-left]:hover)) *) {\n      display: block;\n    }\n  }\n  .group-has-\\[\\[data-resizer-left\\]\\[data-resizing\\=\\\"true\\\"\\]\\]\\/table\\:block {\n    &:is(:where(.group\\/table):has(*:is([data-resizer-left][data-resizing=\"true\"])) *) {\n      display: block;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:bg-accent {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      background-color: var(--accent);\n    }\n  }\n  .group-data-\\[pressed\\=true\\]\\:text-accent-foreground {\n    &:is(:where(.group)[data-pressed=\"true\"] *) {\n      color: var(--accent-foreground);\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .peer-has-\\[\\[role\\=menuitem\\]\\]\\/menu-group\\:block {\n    &:is(:where(.peer\\/menu-group):has(*:is([role=menuitem])) ~ *) {\n      display: block;\n    }\n  }\n  .peer-has-\\[\\[role\\=menuitemcheckbox\\]\\]\\/menu-group\\:block {\n    &:is(:where(.peer\\/menu-group):has(*:is([role=menuitemcheckbox])) ~ *) {\n      display: block;\n    }\n  }\n  .peer-has-\\[\\[role\\=menuitemradio\\]\\]\\/menu-group\\:block {\n    &:is(:where(.peer\\/menu-group):has(*:is([role=menuitemradio])) ~ *) {\n      display: block;\n    }\n  }\n  .peer-has-\\[\\[role\\=option\\]\\]\\/menu-group\\:block {\n    &:is(:where(.peer\\/menu-group):has(*:is([role=option])) ~ *) {\n      display: block;\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:bg-transparent {\n    & *::selection {\n      background-color: transparent;\n    }\n    &::selection {\n      background-color: transparent;\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-background {\n    &::file-selector-button {\n      background-color: var(--background);\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground\\/80 {\n    &::placeholder {\n      color: var(--muted-foreground);\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);\n      }\n    }\n  }\n  .before\\:absolute {\n    &::before {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .before\\:z-10 {\n    &::before {\n      content: var(--tw-content);\n      z-index: 10;\n    }\n  }\n  .before\\:box-border {\n    &::before {\n      content: var(--tw-content);\n      box-sizing: border-box;\n    }\n  }\n  .before\\:size-full {\n    &::before {\n      content: var(--tw-content);\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .before\\:cursor-text {\n    &::before {\n      content: var(--tw-content);\n      cursor: text;\n    }\n  }\n  .before\\:border-t {\n    &::before {\n      content: var(--tw-content);\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .before\\:border-r {\n    &::before {\n      content: var(--tw-content);\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .before\\:border-b {\n    &::before {\n      content: var(--tw-content);\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .before\\:border-l {\n    &::before {\n      content: var(--tw-content);\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .before\\:border-t-border {\n    &::before {\n      content: var(--tw-content);\n      border-top-color: var(--border);\n    }\n  }\n  .before\\:border-r-border {\n    &::before {\n      content: var(--tw-content);\n      border-right-color: var(--border);\n    }\n  }\n  .before\\:border-b-border {\n    &::before {\n      content: var(--tw-content);\n      border-bottom-color: var(--border);\n    }\n  }\n  .before\\:border-l-border {\n    &::before {\n      content: var(--tw-content);\n      border-left-color: var(--border);\n    }\n  }\n  .before\\:opacity-30 {\n    &::before {\n      content: var(--tw-content);\n      opacity: 30%;\n    }\n  }\n  .before\\:content-\\[\\'\\'\\] {\n    &::before {\n      content: var(--tw-content);\n      --tw-content: '';\n      content: var(--tw-content);\n    }\n  }\n  .before\\:content-\\[attr\\(placeholder\\)\\] {\n    &::before {\n      content: var(--tw-content);\n      --tw-content: attr(placeholder);\n      content: var(--tw-content);\n    }\n  }\n  .before\\:select-none {\n    &::before {\n      content: var(--tw-content);\n      -webkit-user-select: none;\n      user-select: none;\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:inset-0 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:-top-0\\.5 {\n    &::after {\n      content: var(--tw-content);\n      top: calc(var(--spacing) * -0.5);\n    }\n  }\n  .after\\:-left-1 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(var(--spacing) * -1);\n    }\n  }\n  .after\\:z-1 {\n    &::after {\n      content: var(--tw-content);\n      z-index: 1;\n    }\n  }\n  .after\\:block {\n    &::after {\n      content: var(--tw-content);\n      display: block;\n    }\n  }\n  .after\\:flex {\n    &::after {\n      content: var(--tw-content);\n      display: flex;\n    }\n  }\n  .after\\:h-16 {\n    &::after {\n      content: var(--tw-content);\n      height: calc(var(--spacing) * 16);\n    }\n  }\n  .after\\:h-\\[calc\\(100\\%\\)\\+4px\\] {\n    &::after {\n      content: var(--tw-content);\n      height: calc(100%)+4px;\n    }\n  }\n  .after\\:w-\\[3px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 3px;\n    }\n  }\n  .after\\:w-\\[calc\\(100\\%\\+8px\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      width: calc(100% + 8px);\n    }\n  }\n  .after\\:rounded-\\[6px\\] {\n    &::after {\n      content: var(--tw-content);\n      border-radius: 6px;\n    }\n  }\n  .after\\:rounded-sm {\n    &::after {\n      content: var(--tw-content);\n      border-radius: calc(var(--radius) - 4px);\n    }\n  }\n  .after\\:bg-neutral-500\\/10 {\n    &::after {\n      content: var(--tw-content);\n      background-color: color-mix(in srgb, oklch(55.6% 0 0) 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-neutral-500) 10%, transparent);\n      }\n    }\n  }\n  .after\\:bg-ring {\n    &::after {\n      content: var(--tw-content);\n      background-color: var(--ring);\n    }\n  }\n  .after\\:pb-\\[var\\(--aspect-ratio\\)\\] {\n    &::after {\n      content: var(--tw-content);\n      padding-bottom: var(--aspect-ratio);\n    }\n  }\n  .after\\:opacity-0 {\n    &::after {\n      content: var(--tw-content);\n      opacity: 0%;\n    }\n  }\n  .after\\:content-\\[\\\"\\\"\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-content: \"\";\n      content: var(--tw-content);\n    }\n  }\n  .after\\:content-\\[\\'_\\'\\] {\n    &::after {\n      content: var(--tw-content);\n      --tw-content: ' ';\n      content: var(--tw-content);\n    }\n  }\n  .group-hover\\:after\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          opacity: 100%;\n        }\n      }\n    }\n  }\n  .focus-within\\:relative {\n    &:focus-within {\n      position: relative;\n    }\n  }\n  .focus-within\\:z-20 {\n    &:focus-within {\n      z-index: 20;\n    }\n  }\n  .focus-within\\:ring-2 {\n    &:focus-within {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-within\\:ring-ring {\n    &:focus-within {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-within\\:ring-offset-2 {\n    &:focus-within {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .hover\\:w-\\[106px\\] {\n    &:hover {\n      @media (hover: hover) {\n        width: 106px;\n      }\n    }\n  }\n  .hover\\:scale-125 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 125%;\n        --tw-scale-y: 125%;\n        --tw-scale-z: 125%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-muted {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-slate-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-slate-700);\n      }\n    }\n  }\n  .hover\\:bg-transparent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: transparent;\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:text-white\\! {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white) !important;\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:bg-primary {\n    &:focus {\n      background-color: var(--primary);\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:text-primary-foreground {\n    &:focus {\n      color: var(--primary-foreground);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-0 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-transparent {\n    &:focus-visible {\n      --tw-ring-color: transparent;\n    }\n  }\n  .focus-visible\\:ring-offset-0 {\n    &:focus-visible {\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-hidden {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .focus-visible\\:outline-ring {\n    &:focus-visible {\n      outline-color: var(--ring);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:cursor-grabbing {\n    &:active {\n      cursor: grabbing;\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .has-aria-disabled\\:border-input {\n    &:has(*[aria-disabled=\"true\"]) {\n      border-color: var(--input);\n    }\n  }\n  .has-aria-disabled\\:bg-muted {\n    &:has(*[aria-disabled=\"true\"]) {\n      background-color: var(--muted);\n    }\n  }\n  .has-data-readonly\\:w-fit {\n    &:has(*[data-readonly]) {\n      width: fit-content;\n    }\n  }\n  .has-data-readonly\\:cursor-default {\n    &:has(*[data-readonly]) {\n      cursor: default;\n    }\n  }\n  .has-data-readonly\\:border-transparent {\n    &:has(*[data-readonly]) {\n      border-color: transparent;\n    }\n  }\n  .has-data-readonly\\:focus-within\\:\\[box-shadow\\:none\\] {\n    &:has(*[data-readonly]) {\n      &:focus-within {\n        box-shadow: none;\n      }\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-\\[\\[data-slate-editor\\]\\:focus\\]\\:ring-2 {\n    &:has(*:is([data-slate-editor]:focus)) {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .has-\\[\\[role\\=menuitem\\]\\]\\:block {\n    &:has(*:is([role=menuitem])) {\n      display: block;\n    }\n  }\n  .has-\\[\\[role\\=menuitemcheckbox\\]\\]\\:block {\n    &:has(*:is([role=menuitemcheckbox])) {\n      display: block;\n    }\n  }\n  .has-\\[\\[role\\=menuitemradio\\]\\]\\:block {\n    &:has(*:is([role=menuitemradio])) {\n      display: block;\n    }\n  }\n  .has-\\[\\[role\\=option\\]\\]\\:block {\n    &:has(*:is([role=option])) {\n      display: block;\n    }\n  }\n  .has-\\[button\\]\\:flex {\n    &:has(*:is(button)) {\n      display: flex;\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:pointer-events-none {\n    &:has(+input:not(:placeholder-shown)) {\n      pointer-events: none;\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:top-0 {\n    &:has(+input:not(:placeholder-shown)) {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:cursor-default {\n    &:has(+input:not(:placeholder-shown)) {\n      cursor: default;\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:text-xs {\n    &:has(+input:not(:placeholder-shown)) {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:font-medium {\n    &:has(+input:not(:placeholder-shown)) {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .has-\\[\\+input\\:not\\(\\:placeholder-shown\\)\\]\\:text-foreground {\n    &:has(+input:not(:placeholder-shown)) {\n      color: var(--foreground);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-checked\\:bg-accent {\n    &[aria-checked=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .aria-checked\\:text-accent-foreground {\n    &[aria-checked=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .aria-selected\\:bg-accent {\n    &[aria-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .aria-selected\\:bg-accent\\/50 {\n    &[aria-selected=\"true\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .aria-selected\\:text-accent-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .aria-selected\\:text-muted-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .aria-selected\\:opacity-30 {\n    &[aria-selected=\"true\"] {\n      opacity: 30%;\n    }\n  }\n  .aria-selected\\:opacity-100 {\n    &[aria-selected=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-disabled\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-disabled\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-readonly\\:w-fit {\n    &[data-readonly] {\n      width: fit-content;\n    }\n  }\n  .\\*\\*\\:data-slate-placeholder\\:top-\\[auto_\\!important\\] {\n    :is(& *) {\n      &[data-slate-placeholder] {\n        top: auto !important;\n      }\n    }\n  }\n  .\\*\\*\\:data-slate-placeholder\\:text-muted-foreground\\/80 {\n    :is(& *) {\n      &[data-slate-placeholder] {\n        color: var(--muted-foreground);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--muted-foreground) 80%, transparent);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-slate-placeholder\\:opacity-100\\! {\n    :is(& *) {\n      &[data-slate-placeholder] {\n        opacity: 100% !important;\n      }\n    }\n  }\n  .data-\\[active-item\\=true\\]\\:bg-accent {\n    &[data-active-item=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[active-item\\=true\\]\\:text-accent-foreground {\n    &[data-active-item=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-muted {\n    &[data-active=\"true\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &[data-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:opacity-50 {\n    &[data-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[error\\=true\\]\\:text-destructive {\n    &[data-error=\"true\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[highlighted\\=true\\]\\:bg-accent {\n    &[data-highlighted=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-accent {\n    &[data-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-primary\\/10 {\n    &[data-selected=\"true\"] {\n      background-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n      }\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-accent-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n    &[data-side=\"bottom\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n    &[data-side=\"left\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n    &[data-side=\"right\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n    &[data-side=\"top\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=block-selection\\]\\:left-2 {\n    :is(& > *) {\n      &[data-slot=\"block-selection\"] {\n        left: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-accent-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-out {\n    &[data-state=\"closed\"] {\n      animation: exit var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:opacity-0 {\n    &[data-state=\"closed\"] {\n      opacity: 0%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:fade-out-0 {\n    &[data-state=\"closed\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2 {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: calc(1/2*100%);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\] {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: calc(48%*-1);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &[data-state=\"closed\"] {\n      --tw-exit-scale: .95;\n    }\n  }\n  .data-\\[state\\=on\\]\\:bg-accent {\n    &[data-state=\"on\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=on\\]\\:text-accent-foreground {\n    &[data-state=\"on\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-in {\n    &[data-state=\"open\"] {\n      animation: enter var(--tw-duration,.15s)var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-muted-foreground {\n    &[data-state=\"open\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:fade-in-0 {\n    &[data-state=\"open\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2 {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: calc(1/2*100%);\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\] {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: calc(48%*-1);\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-95 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: .95;\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .supports-backdrop-blur\\:bg-background\\/60 {\n    @supports (backdrop-blur: var(--tw)) {\n      background-color: var(--background);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--background) 60%, transparent);\n      }\n    }\n  }\n  .max-sm\\:hidden {\n    @media (width < 40rem) {\n      display: none;\n    }\n  }\n  .sm\\:mt-0 {\n    @media (width >= 40rem) {\n      margin-top: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:w-auto {\n    @media (width >= 40rem) {\n      width: auto;\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:items-center {\n    @media (width >= 40rem) {\n      align-items: center;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:space-y-0 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-2 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:space-x-4 {\n    @media (width >= 40rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .sm\\:rounded-lg {\n    @media (width >= 40rem) {\n      border-radius: var(--radius);\n    }\n  }\n  .sm\\:px-24 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 24);\n    }\n  }\n  .sm\\:px-\\[max\\(64px\\,calc\\(50\\%-350px\\)\\)\\] {\n    @media (width >= 40rem) {\n      padding-inline: max(64px, calc(50% - 350px));\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .sm\\:opacity-0 {\n    @media (width >= 40rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:flex-row {\n    @media (width >= 48rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .lg\\:inline {\n    @media (width >= 64rem) {\n      display: inline;\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .\\32 xl\\:max-w-\\[96rem\\] {\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .dark\\:scale-0 {\n    &:is(.dark *) {\n      --tw-scale-x: 0%;\n      --tw-scale-y: 0%;\n      --tw-scale-z: 0%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:scale-100 {\n    &:is(.dark *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:-rotate-90 {\n    &:is(.dark *) {\n      rotate: calc(90deg * -1);\n    }\n  }\n  .dark\\:rotate-0 {\n    &:is(.dark *) {\n      rotate: 0deg;\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--input);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-purple-900 {\n    &:is(.dark *) {\n      background-color: var(--color-purple-900);\n    }\n  }\n  .dark\\:bg-red-900 {\n    &:is(.dark *) {\n      background-color: var(--color-red-900);\n    }\n  }\n  .dark\\:text-muted-foreground {\n    &:is(.dark *) {\n      color: var(--muted-foreground);\n    }\n  }\n  .dark\\:text-purple-400 {\n    &:is(.dark *) {\n      color: var(--color-purple-400);\n    }\n  }\n  .dark\\:text-red-400 {\n    &:is(.dark *) {\n      color: var(--color-red-400);\n    }\n  }\n  .dark\\:shadow-\\[rgba\\(255\\,_255\\,_255\\,_0\\.1\\)_0px_0\\.5px_0px_0px_inset\\,_rgb\\(26\\,_29\\,_30\\)_0px_1px_5px_0px_inset\\,_rgb\\(76\\,_81\\,_85\\)_0px_0px_0px_0\\.5px\\,_rgb\\(76\\,_81\\,_85\\)_0px_2px_1px_-1px\\,_rgb\\(76\\,_81\\,_85\\)_0px_1px_0px_0px\\] {\n    &:is(.dark *) {\n      --tw-shadow-color: rgba(255, 255, 255, 0.1);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, rgba(255, 255, 255, 0.1) 0px 0.5px 0px 0px inset, rgb(26, 29, 30) 0px 1px 5px 0px inset, rgb(76, 81, 85) 0px 0px 0px 0.5px, rgb(76, 81, 85) 0px 2px 1px -1px, rgb(76, 81, 85) 0px 1px 0px 0px var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:border-input {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        border-color: var(--input);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:text-foreground {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .print\\:hidden {\n    @media print {\n      display: none;\n    }\n  }\n  .print\\:break-inside-avoid {\n    @media print {\n      break-inside: avoid;\n    }\n  }\n  .print\\:placeholder\\:text-transparent {\n    @media print {\n      &::placeholder {\n        color: transparent;\n      }\n    }\n  }\n  .\\[\\&_\\.katex-display\\]\\:my-0 {\n    & .katex-display {\n      margin-block: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\.react-tweet-theme\\]\\:my-0 {\n    & .react-tweet-theme {\n      margin-block: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\.react-tweet-theme\\]\\:ring-2 {\n    & .react-tweet-theme {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .\\[\\&_\\.react-tweet-theme\\]\\:ring-ring {\n    & .react-tweet-theme {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .\\[\\&_\\.react-tweet-theme\\]\\:ring-offset-2 {\n    & .react-tweet-theme {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .\\[\\&_\\.slate-selection-area\\]\\:z-50 {\n    & .slate-selection-area {\n      z-index: 50;\n    }\n  }\n  .\\[\\&_\\.slate-selection-area\\]\\:border {\n    & .slate-selection-area {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:absolute {\n    & > .lty-playbtn {\n      position: absolute;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:top-1\\/2 {\n    & > .lty-playbtn {\n      top: calc(1/2 * 100%);\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:left-1\\/2 {\n    & > .lty-playbtn {\n      left: calc(1/2 * 100%);\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:z-1 {\n    & > .lty-playbtn {\n      z-index: 1;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:h-\\[46px\\] {\n    & > .lty-playbtn {\n      height: 46px;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:w-\\[70px\\] {\n    & > .lty-playbtn {\n      width: 70px;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:\\[transform\\:translate3d\\(-50\\%\\,-50\\%\\,0\\)\\] {\n    & > .lty-playbtn {\n      transform: translate3d(-50%,-50%,0);\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:rounded-\\[14\\%\\] {\n    & > .lty-playbtn {\n      border-radius: 14%;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:bg-\\[\\#212121\\] {\n    & > .lty-playbtn {\n      background-color: #212121;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:opacity-80 {\n    & > .lty-playbtn {\n      opacity: 80%;\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:\\[transition\\:all_0\\.2s_cubic-bezier\\(0\\,_0\\,_0\\.2\\,_1\\)\\] {\n    & > .lty-playbtn {\n      transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:absolute {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        position: absolute;\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:top-1\\/2 {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        top: calc(1/2 * 100%);\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:left-1\\/2 {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        left: calc(1/2 * 100%);\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:\\[transform\\:translate3d\\(-50\\%\\,-50\\%\\,0\\)\\] {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        transform: translate3d(-50%,-50%,0);\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:border-y-\\[11px\\] {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        border-block-style: var(--tw-border-style);\n        border-block-width: 11px;\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:border-r-0 {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        border-right-style: var(--tw-border-style);\n        border-right-width: 0px;\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:border-l-\\[19px\\] {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        border-left-style: var(--tw-border-style);\n        border-left-width: 19px;\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:border-\\[transparent_transparent_transparent_\\#fff\\] {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        border-color: transparent transparent transparent #fff;\n      }\n    }\n  }\n  .\\[\\&_\\>_\\.lty-playbtn\\]\\:before\\:content-\\[\\\"\\\"\\] {\n    & > .lty-playbtn {\n      &::before {\n        content: var(--tw-content);\n        --tw-content: \"\";\n        content: var(--tw-content);\n      }\n    }\n  }\n  .\\[\\&_\\>_iframe\\]\\:absolute {\n    & > iframe {\n      position: absolute;\n    }\n  }\n  .\\[\\&_\\>_iframe\\]\\:top-0 {\n    & > iframe {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\>_iframe\\]\\:left-0 {\n    & > iframe {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\>_iframe\\]\\:size-full {\n    & > iframe {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {\n    & [cmdk-group-heading] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {\n    & [cmdk-group-heading] {\n      padding-block: calc(var(--spacing) * 1.5);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {\n    & [cmdk-group-heading] {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {\n    & [cmdk-group-heading] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {\n    & [cmdk-group-heading] {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {\n    & [cmdk-group] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {\n    & [cmdk-group]:not([hidden]) ~[cmdk-group] {\n      padding-top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:size-5 {\n    & [cmdk-input-wrapper] svg {\n      width: calc(var(--spacing) * 5);\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {\n    & [cmdk-input-wrapper] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {\n    & [cmdk-input-wrapper] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {\n    & [cmdk-input] {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {\n    & [cmdk-item] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {\n    & [cmdk-item] {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:size-5 {\n    & [cmdk-item] svg {\n      width: calc(var(--spacing) * 5);\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {\n    & [cmdk-item] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {\n    & [cmdk-item] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_strong\\]\\:font-bold {\n    & strong {\n      --tw-font-weight: var(--font-weight-bold);\n      font-weight: var(--font-weight-bold);\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:hidden {\n    & svg {\n      display: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:size-4 {\n    & svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\]\\:size-6 {\n    & svg {\n      width: calc(var(--spacing) * 6);\n      height: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\]\\:text-muted-foreground {\n    & svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[data-icon\\]\\)\\]\\:size-4 {\n    & svg:not([data-icon]) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:cursor-\\[unset\\] {\n    &.lyt-activated {\n      cursor: unset;\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:pointer-events-none {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        pointer-events: none;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:absolute {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        position: absolute;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:top-0 {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        top: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:h-\\[60px\\] {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        height: 60px;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:w-full {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:bg-\\[url\\(data\\:image\\/png\\;base64\\,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT\\+OqFAAAAdklEQVQoz42QQQ7AIAgEF\\/T\\/D\\+kbq\\/RWAlnQyyazA4aoAB4FsBSA\\/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg\\=\\=\\)\\] {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAADGCAYAAAAT+OqFAAAAdklEQVQoz42QQQ7AIAgEF/T/D+kbq/RWAlnQyyazA4aoAB4FsBSA/bFjuF1EOL7VbrIrBuusmrt4ZZORfb6ehbWdnRHEIiITaEUKa5EJqUakRSaEYBJSCY2dEstQY7AuxahwXFrvZmWl2rh4JZ07z9dLtesfNj5q0FU3A5ObbwAAAABJRU5ErkJggg==);\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:bg-top {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        background-position: top;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:bg-repeat-x {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        background-repeat: repeat-x;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:pb-\\[50px\\] {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        padding-bottom: 50px;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:opacity-0 {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        opacity: 0%;\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated\\]\\:before\\:\\[transition\\:all_0\\.2s_cubic-bezier\\(0\\,_0\\,_0\\.2\\,_1\\)\\] {\n    &.lyt-activated {\n      &::before {\n        content: var(--tw-content);\n        transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);\n      }\n    }\n  }\n  .\\[\\&\\.lyt-activated_\\>_\\.lty-playbtn\\]\\:pointer-events-none {\n    &.lyt-activated > .lty-playbtn {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\.lyt-activated_\\>_\\.lty-playbtn\\]\\:opacity-0\\! {\n    &.lyt-activated > .lty-playbtn {\n      opacity: 0% !important;\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar\\]\\:w-4 {\n    &::-webkit-scrollbar {\n      width: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-button\\]\\:hidden {\n    &::-webkit-scrollbar-button {\n      display: none;\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-button\\]\\:size-0 {\n    &::-webkit-scrollbar-button {\n      width: calc(var(--spacing) * 0);\n      height: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:min-h-11 {\n    &::-webkit-scrollbar-thumb {\n      min-height: calc(var(--spacing) * 11);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:rounded-full {\n    &::-webkit-scrollbar-thumb {\n      border-radius: calc(infinity * 1px);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:border-4 {\n    &::-webkit-scrollbar-thumb {\n      border-style: var(--tw-border-style);\n      border-width: 4px;\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:border-solid {\n    &::-webkit-scrollbar-thumb {\n      --tw-border-style: solid;\n      border-style: solid;\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:border-popover {\n    &::-webkit-scrollbar-thumb {\n      border-color: var(--popover);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:bg-muted {\n    &::-webkit-scrollbar-thumb {\n      background-color: var(--muted);\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:bg-clip-padding {\n    &::-webkit-scrollbar-thumb {\n      background-clip: padding-box;\n    }\n  }\n  .\\[\\&\\:\\:-webkit-scrollbar-thumb\\]\\:hover\\:bg-muted-foreground\\/25 {\n    &::-webkit-scrollbar-thumb {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--muted-foreground);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--muted-foreground) 25%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .focus\\:\\[\\&\\:\\:placeholder\\]\\:opacity-0 {\n    &:focus {\n      &::placeholder {\n        opacity: 0%;\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent {\n    &:has([aria-selected]) {\n      background-color: var(--accent);\n    }\n  }\n  .first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md {\n    &:first-child {\n      &:has([aria-selected]) {\n        border-top-left-radius: calc(var(--radius) - 2px);\n        border-bottom-left-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md {\n    &:last-child {\n      &:has([aria-selected]) {\n        border-top-right-radius: calc(var(--radius) - 2px);\n        border-bottom-right-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50 {\n    &:has([aria-selected].day-outside) {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has([aria-selected].day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=option\\]\\)\\]\\:block {\n    &:has([role=option]) {\n      display: block;\n    }\n  }\n  .\\[\\&\\:hover_\\>_\\.lty-playbtn\\]\\:bg-\\[red\\] {\n    &:hover > .lty-playbtn {\n      background-color: red;\n    }\n  }\n  .\\[\\&\\:hover_\\>_\\.lty-playbtn\\]\\:opacity-100 {\n    &:hover > .lty-playbtn {\n      opacity: 100%;\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-addition\\]\\:bg-\\[\\#f0fff4\\] {\n    :is(& *) {\n      &:is(.hljs-addition) {\n        background-color: #f0fff4;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-addition\\]\\:text-\\[\\#22863a\\] {\n    :is(& *) {\n      &:is(.hljs-addition) {\n        color: #22863a;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-attr\\,\\.hljs-attribute\\,\\.hljs-literal\\,\\.hljs-meta\\,\\.hljs-number\\,\\.hljs-operator\\,\\.hljs-selector-attr\\,\\.hljs-selector-class\\,\\.hljs-selector-id\\,\\.hljs-variable\\]\\:text-\\[\\#005cc5\\] {\n    :is(& *) {\n      &:is(.hljs-attr,.hljs-attribute,.hljs-literal,.hljs-meta,.hljs-number,.hljs-operator,.hljs-selector-attr,.hljs-selector-class,.hljs-selector-id,.hljs-variable) {\n        color: #005cc5;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-built_in\\,\\.hljs-symbol\\]\\:text-\\[\\#e36209\\] {\n    :is(& *) {\n      &:is(.hljs-built in,.hljs-symbol) {\n        color: #e36209;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-bullet\\]\\:text-\\[\\#735c0f\\] {\n    :is(& *) {\n      &:is(.hljs-bullet) {\n        color: #735c0f;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-comment\\,\\.hljs-code\\,\\.hljs-formula\\]\\:text-\\[\\#6a737d\\] {\n    :is(& *) {\n      &:is(.hljs-comment,.hljs-code,.hljs-formula) {\n        color: #6a737d;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-deletion\\]\\:bg-\\[\\#ffeef0\\] {\n    :is(& *) {\n      &:is(.hljs-deletion) {\n        background-color: #ffeef0;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-deletion\\]\\:text-\\[\\#b31d28\\] {\n    :is(& *) {\n      &:is(.hljs-deletion) {\n        color: #b31d28;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-emphasis\\]\\:italic {\n    :is(& *) {\n      &:is(.hljs-emphasis) {\n        font-style: italic;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-keyword\\,\\.hljs-doctag\\,\\.hljs-template-tag\\,\\.hljs-template-variable\\,\\.hljs-type\\,\\.hljs-variable\\.language_\\]\\:text-\\[\\#d73a49\\] {\n    :is(& *) {\n      &:is(.hljs-keyword,.hljs-doctag,.hljs-template-tag,.hljs-template-variable,.hljs-type,.hljs-variable.language ) {\n        color: #d73a49;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-name\\,\\.hljs-quote\\,\\.hljs-selector-tag\\,\\.hljs-selector-pseudo\\]\\:text-\\[\\#22863a\\] {\n    :is(& *) {\n      &:is(.hljs-name,.hljs-quote,.hljs-selector-tag,.hljs-selector-pseudo) {\n        color: #22863a;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-regexp\\,\\.hljs-string\\,\\.hljs-meta_\\.hljs-string\\]\\:text-\\[\\#032f62\\] {\n    :is(& *) {\n      &:is(.hljs-regexp,.hljs-string,.hljs-meta .hljs-string) {\n        color: #032f62;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-section\\]\\:font-bold {\n    :is(& *) {\n      &:is(.hljs-section) {\n        --tw-font-weight: var(--font-weight-bold);\n        font-weight: var(--font-weight-bold);\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-section\\]\\:text-\\[\\#005cc5\\] {\n    :is(& *) {\n      &:is(.hljs-section) {\n        color: #005cc5;\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-strong\\]\\:font-bold {\n    :is(& *) {\n      &:is(.hljs-strong) {\n        --tw-font-weight: var(--font-weight-bold);\n        font-weight: var(--font-weight-bold);\n      }\n    }\n  }\n  .\\*\\*\\:\\[\\.hljs-title\\,\\.hljs-title\\.class_\\,\\.hljs-title\\.class_\\.inherited__\\,\\.hljs-title\\.function_\\]\\:text-\\[\\#6f42c1\\] {\n    :is(& *) {\n      &:is(.hljs-title,.hljs-title.class ,.hljs-title.class .inherited  ,.hljs-title.function ) {\n        color: #6f42c1;\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n}\n:root {\n  --radius: 0.625rem;\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.145 0 0);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.145 0 0);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.145 0 0);\n  --primary: oklch(0.205 0 0);\n  --primary-foreground: oklch(0.985 0 0);\n  --secondary: oklch(0.97 0 0);\n  --secondary-foreground: oklch(0.205 0 0);\n  --muted: oklch(0.97 0 0);\n  --muted-foreground: oklch(0.556 0 0);\n  --accent: oklch(0.97 0 0);\n  --accent-foreground: oklch(0.205 0 0);\n  --destructive: oklch(0.577 0.245 27.325);\n  --border: oklch(0.922 0 0);\n  --input: oklch(0.922 0 0);\n  --ring: oklch(0.708 0 0);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --sidebar: oklch(0.985 0 0);\n  --sidebar-foreground: oklch(0.145 0 0);\n  --sidebar-primary: oklch(0.205 0 0);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.97 0 0);\n  --sidebar-accent-foreground: oklch(0.205 0 0);\n  --sidebar-border: oklch(0.922 0 0);\n  --sidebar-ring: oklch(0.708 0 0);\n}\n.dark {\n  --background: oklch(0.145 0 0);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.205 0 0);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.205 0 0);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.922 0 0);\n  --primary-foreground: oklch(0.205 0 0);\n  --secondary: oklch(0.269 0 0);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.269 0 0);\n  --muted-foreground: oklch(0.708 0 0);\n  --accent: oklch(0.269 0 0);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.704 0.191 22.216);\n  --border: oklch(1 0 0 / 10%);\n  --input: oklch(1 0 0 / 15%);\n  --ring: oklch(0.556 0 0);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.205 0 0);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.488 0.243 264.376);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.269 0 0);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(1 0 0 / 10%);\n  --sidebar-ring: oklch(0.556 0 0);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;AACA;;AACA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEF;EACE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAIF;EACE;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAME;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAGA;;;;EAKA;;;;EAGA;;;;EAKA;;;;EAGA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAOA;;;;;;EAQE;IACE;;;;;;EAQJ;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAOE;IAAuB;;;;;EAOvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAOzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAMF;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;IAAsC;;;;IAEpC;MAAgD;;;;;;EAMlD;IAAwB;;;;;EAKxB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;;;;;;;EAQA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAQ9C;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAQE;;;;EAEE;IAAgD;;;;;EAQtD;IAAc;;;;;EAKd;IAAc;;;;;EAKd;IACE;;;;;EAMF;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;;EASA;;;;;;EASA;;;;;EAQA;;;;;;EAQF;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAMF;;;;EAME;;;;;EAQA;;;;;EAOF;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;;EAQA;;;;EAOA;;;;;EAQA;;;;EAQE;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;;EAOE;;;;;;AAON;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;EACE;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;;AAKF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AAMA;EACE;IACE", "debugId": null}}, {"offset": {"line": 5464, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/styles/content-styles.css"], "sourcesContent": ["/* Стили для отображения HTML-контента */\n.content-html h1 {\n  font-size: 1.75rem;\n  font-weight: 700;\n  margin: 0.75rem 0;\n  color: rgb(5, 5, 5);\n}\n\n.content-html h2 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  margin: 0.75rem 0;\n  color: rgb(5, 5, 5);\n}\n\n.content-html h3 {\n  font-size: 1.25rem;\n  font-weight: 700;\n  margin: 0.75rem 0;\n  color: rgb(5, 5, 5);\n}\n\n.content-html blockquote {\n  border-left: 4px solid #ccc;\n  margin: 1rem 0;\n  padding-left: 1rem;\n  color: rgb(101, 103, 107);\n  font-style: italic;\n}\n\n.content-html ul {\n  list-style-type: disc;\n  margin: 1rem 0;\n  padding-left: 2rem;\n}\n\n.content-html ol {\n  list-style-type: decimal;\n  margin: 1rem 0;\n  padding-left: 2rem;\n}\n\n.content-html li {\n  margin: 0.25rem 0;\n}\n\n.content-html a {\n  color: rgb(33, 111, 219);\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n.content-html p {\n  margin: 0.75rem 0;\n}\n\n.content-html strong,\n.content-html b {\n  font-weight: bold;\n}\n\n.content-html em,\n.content-html i {\n  font-style: italic;\n}\n\n.content-html u {\n  text-decoration: underline;\n}\n\n.content-html s,\n.content-html strike {\n  text-decoration: line-through;\n}\n\n.content-html img {\n  max-width: 100%;\n  height: auto;\n  margin: 1rem 0;\n}\n"], "names": [], "mappings": "AACA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;AAQA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAIA;;;;AAKA", "debugId": null}}]}