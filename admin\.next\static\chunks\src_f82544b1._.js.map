{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,uYAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,uYAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,uYAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/api/works.ts"], "sourcesContent": ["import { getSession } from \"next-auth/react\";\r\nimport { gql } from \"graphql-tag\";\r\nimport {\r\n  GetWorksQuery,\r\n  GetWorksQueryVariables,\r\n  GetWorkQuery,\r\n  GetWorkQueryVariables,\r\n  CreateWorkMutation,\r\n  CreateWorkMutationVariables,\r\n  UpdateWorkMutation,\r\n  UpdateWorkMutationVariables,\r\n  DeleteWorkMutation,\r\n  DeleteWorkMutationVariables,\r\n  Work,\r\n  CreateWorkInput,\r\n  UpdateWorkInput\r\n} from \"@/lib/graphql/generated/graphql\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3556\";\r\n\r\nasync function fetchWithAuth(url: string, options: RequestInit = {}) {\r\n  const session = await getSession();\r\n\r\n  return fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...options.headers,\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${session?.accessToken}`,\r\n    },\r\n  });\r\n}\r\n\r\n// GraphQL запросы и мутации\r\nconst GET_WORKS = gql`\r\n  query GetWorks($skip: Int, $take: Int, $where: WorkFilterInput) {\r\n    works(skip: $skip, take: $take, where: $where) {\r\n      items {\r\n        id\r\n        urlPart\r\n        authorId\r\n        title\r\n        publishedDate\r\n        modifiedDate\r\n      }\r\n      totalCount\r\n    }\r\n  }\r\n`;\r\n\r\nconst GET_WORK = gql`\r\n  query GetWork($id: String!) {\r\n    work(id: $id) {\r\n      id\r\n      urlPart\r\n      authorId\r\n      title\r\n      content\r\n      genres\r\n      comments\r\n      source\r\n      publishedDate\r\n      publishedBy\r\n      modifiedDate\r\n      modifiedBy\r\n    }\r\n  }\r\n`;\r\n\r\nconst CREATE_WORK = gql`\r\n  mutation CreateWork($input: CreateWorkInput!) {\r\n    createWork(input: $input) {\r\n      work {\r\n        id\r\n        urlPart\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst UPDATE_WORK = gql`\r\n  mutation UpdateWork($id: String!, $input: UpdateWorkInput!) {\r\n    updateWork(id: $id, input: $input) {\r\n      work {\r\n        id\r\n        urlPart\r\n        title\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst DELETE_WORK = gql`\r\n  mutation DeleteWork($id: ID!) {\r\n    deleteWork(id: $id) {\r\n      success\r\n    }\r\n  }\r\n`;\r\n\r\nexport async function getWorks(\r\n  page: number = 1,\r\n  pageSize: number = 10,\r\n  filter?: string,\r\n  authorId?: string\r\n): Promise<{ works: Work[], totalCount: number }> {\r\n  const skip = (page - 1) * pageSize;\r\n\r\n  // Создаем объект фильтрации для GraphQL запроса\r\n  let where: any = undefined;\r\n\r\n  if (filter || authorId) {\r\n    where = {};\r\n\r\n    if (filter) {\r\n      where.title = { contains: filter };\r\n    }\r\n\r\n    if (authorId) {\r\n      where.authorId = { eq: authorId };\r\n    }\r\n  }\r\n\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_WORKS.loc?.source.body,\r\n      variables: { skip, take: pageSize, where }\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetWorksQuery;\r\n  return {\r\n    works: result.works.items as Work[],\r\n    totalCount: result.works.totalCount\r\n  };\r\n}\r\n\r\nexport async function getWork(id: string): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_WORK.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetWorkQuery;\r\n  return result.work as Work;\r\n}\r\n\r\nexport async function createWork(input: CreateWorkInput): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: CREATE_WORK.loc?.source.body,\r\n      variables: { input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as CreateWorkMutation;\r\n  return result.createWork.work as Work;\r\n}\r\n\r\nexport async function updateWork(id: string, input: UpdateWorkInput): Promise<Work> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: UPDATE_WORK.loc?.source.body,\r\n      variables: { id, input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as UpdateWorkMutation;\r\n  return result.updateWork.work as Work;\r\n}\r\n\r\nexport async function deleteWork(id: string): Promise<boolean> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: DELETE_WORK.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as DeleteWorkMutation;\r\n  return result.deleteWork.success;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAkBgB;AAlBhB;AACA;;;AAiBA,MAAM,UAAU,6DAAmC;AAEnD,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACjE,MAAM,UAAU,MAAM,CAAA,GAAA,sWAAA,CAAA,aAAU,AAAD;IAE/B,OAAO,MAAM,KAAK;QAChB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,QAAQ,OAAO;YAClB,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,SAAS,aAAa;QACjD;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,YAAY,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;AActB,CAAC;AAED,MAAM,WAAW,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;AAiBrB,CAAC;AAED,MAAM,cAAc,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAUxB,CAAC;AAED,MAAM,cAAc,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAUxB,CAAC;AAED,MAAM,cAAc,iJAAA,CAAA,MAAG,CAAC;;;;;;AAMxB,CAAC;AAEM,eAAe,SACpB,OAAe,CAAC,EAChB,WAAmB,EAAE,EACrB,MAAe,EACf,QAAiB;IAEjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,gDAAgD;IAChD,IAAI,QAAa;IAEjB,IAAI,UAAU,UAAU;QACtB,QAAQ,CAAC;QAET,IAAI,QAAQ;YACV,MAAM,KAAK,GAAG;gBAAE,UAAU;YAAO;QACnC;QAEA,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;gBAAE,IAAI;YAAS;QAClC;IACF;IAEA,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,UAAU,GAAG,EAAE,OAAO;YAC7B,WAAW;gBAAE;gBAAM,MAAM;gBAAU;YAAM;QAC3C;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO;QACL,OAAO,OAAO,KAAK,CAAC,KAAK;QACzB,YAAY,OAAO,KAAK,CAAC,UAAU;IACrC;AACF;AAEO,eAAe,QAAQ,EAAU;IACtC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,SAAS,GAAG,EAAE,OAAO;YAC5B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,IAAI;AACpB;AAEO,eAAe,WAAW,KAAsB;IACrD,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;YAAM;QACrB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,IAAI;AAC/B;AAEO,eAAe,WAAW,EAAU,EAAE,KAAsB;IACjE,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;gBAAI;YAAM;QACzB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,IAAI;AAC/B;AAEO,eAAe,WAAW,EAAU;IACzC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,UAAU,CAAC,OAAO;AAClC", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/hooks/use-works.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getWorks, getWork, createWork, updateWork, deleteWork } from \"@/lib/api/works\";\r\nimport { Work, CreateWorkInput, UpdateWorkInput } from \"@/lib/graphql/generated/graphql\";\r\n\r\nexport function useWorks(page: number = 1, pageSize: number = 10, filter?: string, authorId?: string) {\r\n  return useQuery({\r\n    queryKey: [\"works\", { page, pageSize, filter, authorId }],\r\n    queryFn: () => getWorks(page, pageSize, filter, authorId),\r\n    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд\r\n    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель\r\n  });\r\n}\r\n\r\nexport function useWork(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"works\", id],\r\n    queryFn: () => getWork(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateWorkInput) => createWork(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: string; data: UpdateWorkInput }) =>\r\n      updateWork(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"works\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteWork() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => deleteWork(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"works\"] });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;;AAGO,SAAS,SAAS,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe,EAAE,QAAiB;;IAClG,OAAO,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;gBAAE;gBAAM;gBAAU;gBAAQ;YAAS;SAAE;QACzD,OAAO;iCAAE,IAAM,CAAA,GAAA,6HAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU,QAAQ;;QAChD,WAAW;QACX,eAAe;iCAAE,CAAC,eAAiB;;IACrC;AACF;GAPgB;;QACP,8QAAA,CAAA,WAAQ;;;AAQV,SAAS,QAAQ,EAAU;;IAChC,OAAO,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAG;QACvB,OAAO;gCAAE,IAAM,CAAA,GAAA,6HAAA,CAAA,UAAO,AAAD,EAAE;;QACvB,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8QAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAA0B,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE;;QAClD,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;YACtD;;IACF;AACF;IATgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW;;;AAQb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAyC,GAC9D,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE,IAAI;;QACjB,SAAS;yCAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;gBACpD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAS,UAAU,EAAE;qBAAC;gBAAC;YACpE;;IACF;AACF;IAXgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW;;;AAUb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,KAAe,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD,EAAE;;QACvC,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;YACtD;;IACF;AACF;IATgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/format-date.ts"], "sourcesContent": ["// Названия месяцев на татарском языке\nconst tatarMonths = [\n  'гыйнвар', // январь\n  'февраль', // февраль\n  'март', // март\n  'апрель', // апрель\n  'май', // май\n  'июнь', // июнь\n  'июль', // июль\n  'август', // август\n  'сентябрь', // сентябрь\n  'октябрь', // октябрь\n  'ноябрь', // ноябрь\n  'декабрь', // декабрь\n];\n\nexport function formatDate(dateString: string): string {\n  if (!dateString) return \"-\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"-\";\n  }\n\n  // Форматируем дату в формате \"день месяц год\"\n  const day = date.getDate();\n  const month = tatarMonths[date.getMonth()];\n  const year = date.getFullYear();\n\n  return `${day} ${month} ${year}`;\n}\n\nexport function formatDateForInput(dateString: string): string {\n  if (!dateString) return \"\";\n\n  const date = new Date(dateString);\n\n  if (isNaN(date.getTime())) {\n    return \"\";\n  }\n\n  return date.toISOString().split(\"T\")[0];\n}\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;AACtC,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,MAAM,KAAK,OAAO;IACxB,MAAM,QAAQ,WAAW,CAAC,KAAK,QAAQ,GAAG;IAC1C,MAAM,OAAO,KAAK,WAAW;IAE7B,OAAO,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM;AAClC;AAEO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IAEtB,IAAI,MAAM,KAAK,OAAO,KAAK;QACzB,OAAO;IACT;IAEA,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AACzC", "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\n          <XIcon />\n          <span className=\"sr-only\">Close</span>\n        </DialogPrimitive.Close>\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,uYAAC,+XAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,uYAAC,+XAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,uYAAC,+XAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,uYAAC,+XAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,uYAAC,+XAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,uYAAC;QAAa,aAAU;;0BACtB,uYAAC;;;;;0BACD,uYAAC,+XAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,uYAAC,+XAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,uYAAC,uRAAA,CAAA,QAAK;;;;;0CACN,uYAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,uYAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,uYAAC,+XAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,uYAAC,+XAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/pagination.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  ChevronsLeft,\n  ChevronsRight,\n} from \"lucide-react\";\n\ninterface PaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n}\n\nexport function Pagination({\n  currentPage,\n  totalPages,\n  onPageChange,\n}: PaginationProps) {\n  // Если страниц меньше 2, не показываем пагинацию\n  if (totalPages <= 1) {\n    return null;\n  }\n\n  // Функция для создания массива страниц для отображения\n  const getPageNumbers = () => {\n    const pageNumbers = [];\n    const maxPagesToShow = 5; // Максимальное количество страниц для отображения\n\n    if (totalPages <= maxPagesToShow) {\n      // Если общее количество страниц меньше или равно maxPagesToShow, показываем все страницы\n      for (let i = 1; i <= totalPages; i++) {\n        pageNumbers.push(i);\n      }\n    } else {\n      // Иначе показываем страницы вокруг текущей\n      let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));\n      let endPage = startPage + maxPagesToShow - 1;\n\n      if (endPage > totalPages) {\n        endPage = totalPages;\n        startPage = Math.max(1, endPage - maxPagesToShow + 1);\n      }\n\n      for (let i = startPage; i <= endPage; i++) {\n        pageNumbers.push(i);\n      }\n    }\n\n    return pageNumbers;\n  };\n\n  const pageNumbers = getPageNumbers();\n\n  return (\n    <div className=\"flex items-center justify-center space-x-2 mt-4\">\n      <Button\n        variant=\"outline\"\n        size=\"icon\"\n        onClick={() => onPageChange(1)}\n        disabled={currentPage === 1}\n        aria-label=\"Первая страница\"\n      >\n        <ChevronsLeft className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"outline\"\n        size=\"icon\"\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage === 1}\n        aria-label=\"Предыдущая страница\"\n      >\n        <ChevronLeft className=\"h-4 w-4\" />\n      </Button>\n\n      {pageNumbers.map((page) => (\n        <Button\n          key={page}\n          variant={currentPage === page ? \"default\" : \"outline\"}\n          size=\"icon\"\n          onClick={() => onPageChange(page)}\n          aria-label={`Страница ${page}`}\n          aria-current={currentPage === page ? \"page\" : undefined}\n        >\n          {page}\n        </Button>\n      ))}\n\n      <Button\n        variant=\"outline\"\n        size=\"icon\"\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage === totalPages}\n        aria-label=\"Следующая страница\"\n      >\n        <ChevronRight className=\"h-4 w-4\" />\n      </Button>\n      <Button\n        variant=\"outline\"\n        size=\"icon\"\n        onClick={() => onPageChange(totalPages)}\n        disabled={currentPage === totalPages}\n        aria-label=\"Последняя страница\"\n      >\n        <ChevronsRight className=\"h-4 w-4\" />\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAgBO,SAAS,WAAW,EACzB,WAAW,EACX,UAAU,EACV,YAAY,EACI;IAChB,iDAAiD;IACjD,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,iBAAiB;QACrB,MAAM,cAAc,EAAE;QACtB,MAAM,iBAAiB,GAAG,kDAAkD;QAE5E,IAAI,cAAc,gBAAgB;YAChC,yFAAyF;YACzF,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,YAAY,IAAI,CAAC;YACnB;QACF,OAAO;YACL,2CAA2C;YAC3C,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB;YACtE,IAAI,UAAU,YAAY,iBAAiB;YAE3C,IAAI,UAAU,YAAY;gBACxB,UAAU;gBACV,YAAY,KAAK,GAAG,CAAC,GAAG,UAAU,iBAAiB;YACrD;YAEA,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;gBACzC,YAAY,IAAI,CAAC;YACnB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,qBACE,uYAAC;QAAI,WAAU;;0BACb,uYAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa;gBAC5B,UAAU,gBAAgB;gBAC1B,cAAW;0BAEX,cAAA,uYAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAE1B,uYAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,gBAAgB;gBAC1B,cAAW;0BAEX,cAAA,uYAAC,2SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;YAGxB,YAAY,GAAG,CAAC,CAAC,qBAChB,uYAAC,qIAAA,CAAA,SAAM;oBAEL,SAAS,gBAAgB,OAAO,YAAY;oBAC5C,MAAK;oBACL,SAAS,IAAM,aAAa;oBAC5B,cAAY,CAAC,SAAS,EAAE,MAAM;oBAC9B,gBAAc,gBAAgB,OAAO,SAAS;8BAE7C;mBAPI;;;;;0BAWT,uYAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,gBAAgB;gBAC1B,cAAW;0BAEX,cAAA,uYAAC,6SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAE1B,uYAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa;gBAC5B,UAAU,gBAAgB;gBAC1B,cAAW;0BAEX,cAAA,uYAAC,+SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIjC;KA9FgB", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/api/authors.ts"], "sourcesContent": ["import { getSession } from \"next-auth/react\";\r\nimport { gql } from \"graphql-tag\";\r\nimport {\r\n  GetAuthorsQuery,\r\n  GetAuthorsQueryVariables,\r\n  GetAuthorQuery,\r\n  GetAuthorQueryVariables,\r\n  CreateAuthorMutation,\r\n  CreateAuthorMutationVariables,\r\n  UpdateAuthorMutation,\r\n  UpdateAuthorMutationVariables,\r\n  DeleteAuthorMutation,\r\n  DeleteAuthorMutationVariables,\r\n  Author,\r\n  CreateAuthorInput,\r\n  UpdateAuthorInput\r\n} from \"@/lib/graphql/generated/graphql\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || \"http://localhost:3556\";\r\n\r\nasync function fetchWithAuth(url: string, options: RequestInit = {}) {\r\n  const session = await getSession();\r\n\r\n  return fetch(url, {\r\n    ...options,\r\n    headers: {\r\n      ...options.headers,\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${session?.accessToken}`,\r\n    },\r\n  });\r\n}\r\n\r\n// GraphQL запросы и мутации\r\nconst GET_AUTHORS = gql`\r\n  query GetAuthors($skip: Int, $take: Int, $where: AuthorFilterInput) {\r\n    authors(skip: $skip, take: $take, where: $where) {\r\n      items {\r\n        id\r\n        urlPart\r\n        displayName\r\n        birthDate\r\n        deathDate\r\n        addedDate\r\n        modifiedDate\r\n      }\r\n      totalCount\r\n    }\r\n  }\r\n`;\r\n\r\nconst GET_AUTHOR = gql`\r\n  query GetAuthor($id: String!) {\r\n    author(id: $id) {\r\n      id\r\n      urlPart\r\n      name\r\n      surName\r\n      lastName\r\n      displayName\r\n      biography\r\n      birthDate\r\n      deathDate\r\n      addedDate\r\n      modifiedDate\r\n    }\r\n  }\r\n`;\r\n\r\nconst CREATE_AUTHOR = gql`\r\n  mutation CreateAuthor($input: CreateAuthorInput!) {\r\n    createAuthor(input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst UPDATE_AUTHOR = gql`\r\n  mutation UpdateAuthor($id: String!, $input: UpdateAuthorInput!) {\r\n    updateAuthor(id: $id, input: $input) {\r\n      author {\r\n        id\r\n        urlPart\r\n        displayName\r\n      }\r\n    }\r\n  }\r\n`;\r\n\r\nconst DELETE_AUTHOR = gql`\r\n  mutation DeleteAuthor($id: String!) {\r\n    deleteAuthor(id: $id) {\r\n      success\r\n    }\r\n  }\r\n`;\r\n\r\nexport async function getAuthors(page: number = 1, pageSize: number = 10, filter?: string): Promise<{ authors: Author[], totalCount: number }> {\r\n  const skip = (page - 1) * pageSize;\r\n\r\n  // Создаем объект фильтрации для GraphQL запроса\r\n  const where = filter ? { displayName: { contains: filter } } : undefined;\r\n\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHORS.loc?.source.body,\r\n      variables: { skip, take: pageSize, where }\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorsQuery;\r\n  return {\r\n    authors: result.authors.items as Author[],\r\n    totalCount: result.authors.totalCount\r\n  };\r\n}\r\n\r\nexport async function getAuthor(id: string): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: GET_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as GetAuthorQuery;\r\n  return result.author as Author;\r\n}\r\n\r\nexport async function createAuthor(input: CreateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: CREATE_AUTHOR.loc?.source.body,\r\n      variables: { input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as CreateAuthorMutation;\r\n  return result.createAuthor.author as Author;\r\n}\r\n\r\nexport async function updateAuthor(id: string, input: UpdateAuthorInput): Promise<Author> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: UPDATE_AUTHOR.loc?.source.body,\r\n      variables: { id, input },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as UpdateAuthorMutation;\r\n  return result.updateAuthor.author as Author;\r\n}\r\n\r\nexport async function deleteAuthor(id: string): Promise<boolean> {\r\n  const response = await fetchWithAuth(`${API_URL}/graphql`, {\r\n    method: \"POST\",\r\n    body: JSON.stringify({\r\n      query: DELETE_AUTHOR.loc?.source.body,\r\n      variables: { id },\r\n    }),\r\n  });\r\n\r\n  const data = await response.json();\r\n\r\n  if (data.errors) {\r\n    throw new Error(data.errors[0].message);\r\n  }\r\n\r\n  const result = data.data as DeleteAuthorMutation;\r\n  return result.deleteAuthor.success;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAkBgB;AAlBhB;AACA;;;AAiBA,MAAM,UAAU,6DAAmC;AAEnD,eAAe,cAAc,GAAW,EAAE,UAAuB,CAAC,CAAC;IACjE,MAAM,UAAU,MAAM,CAAA,GAAA,sWAAA,CAAA,aAAU,AAAD;IAE/B,OAAO,MAAM,KAAK;QAChB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,QAAQ,OAAO;YAClB,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,SAAS,aAAa;QACjD;IACF;AACF;AAEA,4BAA4B;AAC5B,MAAM,cAAc,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;AAexB,CAAC;AAED,MAAM,aAAa,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;AAgBvB,CAAC;AAED,MAAM,gBAAgB,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,iJAAA,CAAA,MAAG,CAAC;;;;;;;;;;AAU1B,CAAC;AAED,MAAM,gBAAgB,iJAAA,CAAA,MAAG,CAAC;;;;;;AAM1B,CAAC;AAEM,eAAe,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;IACvF,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAE1B,gDAAgD;IAChD,MAAM,QAAQ,SAAS;QAAE,aAAa;YAAE,UAAU;QAAO;IAAE,IAAI;IAE/D,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,YAAY,GAAG,EAAE,OAAO;YAC/B,WAAW;gBAAE;gBAAM,MAAM;gBAAU;YAAM;QAC3C;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO;QACL,SAAS,OAAO,OAAO,CAAC,KAAK;QAC7B,YAAY,OAAO,OAAO,CAAC,UAAU;IACvC;AACF;AAEO,eAAe,UAAU,EAAU;IACxC,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,WAAW,GAAG,EAAE,OAAO;YAC9B,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,MAAM;AACtB;AAEO,eAAe,aAAa,KAAwB;IACzD,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAM;QACrB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU,EAAE,KAAwB;IACrE,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;gBAAI;YAAM;QACzB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,MAAM;AACnC;AAEO,eAAe,aAAa,EAAU;IAC3C,MAAM,WAAW,MAAM,cAAc,GAAG,QAAQ,QAAQ,CAAC,EAAE;QACzD,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;YACnB,OAAO,cAAc,GAAG,EAAE,OAAO;YACjC,WAAW;gBAAE;YAAG;QAClB;IACF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAEhC,IAAI,KAAK,MAAM,EAAE;QACf,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,SAAS,KAAK,IAAI;IACxB,OAAO,OAAO,YAAY,CAAC,OAAO;AACpC", "debugId": null}}, {"offset": {"line": 1112, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/hooks/use-authors.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { getAuthors, getAuthor, createAuthor, updateAuthor, deleteAuthor } from \"@/lib/api/authors\";\r\nimport { Author, CreateAuthorInput, UpdateAuthorInput } from \"@/lib/graphql/generated/graphql\";\r\n\r\nexport function useAuthors(page: number = 1, pageSize: number = 10, filter?: string) {\r\n  return useQuery({\r\n    queryKey: [\"authors\", { page, pageSize, filter }],\r\n    queryFn: () => getAuthors(page, pageSize, filter),\r\n    staleTime: 5000, // Данные считаются актуальными в течение 5 секунд\r\n    placeholderData: (previousData) => previousData, // Используем предыдущие данные как заполнитель\r\n  });\r\n}\r\n\r\nexport function useAuthor(id: string) {\r\n  return useQuery({\r\n    queryKey: [\"author\", id],\r\n    queryFn: () => getAuthor(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateAuthorInput) => createAuthor(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: string; data: UpdateAuthorInput }) =>\r\n      updateAuthor(id, data),\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\", variables.id] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteAuthor() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => deleteAuthor(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"authors\"] });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AACA;;;;AAGO,SAAS,WAAW,OAAe,CAAC,EAAE,WAAmB,EAAE,EAAE,MAAe;;IACjF,OAAO,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;gBAAE;gBAAM;gBAAU;YAAO;SAAE;QACjD,OAAO;mCAAE,IAAM,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD,EAAE,MAAM,UAAU;;QAC1C,WAAW;QACX,eAAe;mCAAE,CAAC,eAAiB;;IACrC;AACF;GAPgB;;QACP,8QAAA,CAAA,WAAQ;;;AAQV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8QAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAU;SAAG;QACxB,OAAO;kCAAE,IAAM,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE;;QACzB,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8QAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,OAA4B,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;;QACtD,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;YACxD;;IACF;AACF;IATgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW;;;AAQb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE,IAAI;;QACnB,SAAS;2CAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;gBACtD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAW,UAAU,EAAE;qBAAC;gBAAC;YACtE;;IACF;AACF;IAXgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW;;;AAUb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yRAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iRAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,KAAe,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;;QACzC,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAU;gBAAC;YACxD;;IACF;AACF;IATgB;;QACM,yRAAA,CAAA,iBAAc;QAE3B,iRAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,6XAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,6XAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,uWAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,uYAAC,6XAAA,CAAA,SAAuB;kBACtB,cAAA,uYAAC,6XAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,6XAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1304, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/authors/author-select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useAuthors } from \"@/lib/hooks/use-authors\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\r\nimport { Check, ChevronsUpDown, Search, X } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface AuthorSelectProps {\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport function AuthorSelect({ value, onChange, disabled }: AuthorSelectProps) {\r\n  const [authorSearchTerm, setAuthorSearchTerm] = useState(\"\");\r\n  const [debouncedAuthorSearchTerm, setDebouncedAuthorSearchTerm] = useState(\"\");\r\n  const [openAuthorPopover, setOpenAuthorPopover] = useState(false);\r\n  const [selectedAuthor, setSelectedAuthor] = useState<{ id: string, displayName: string } | null>(null);\r\n  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);\r\n  const authorSearchInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Дебаунсинг для поиска авторов\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedAuthorSearchTerm(authorSearchTerm);\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [authorSearchTerm]);\r\n\r\n  const { data: authorsData, isLoading: isLoadingAuthors } = useAuthors(1, 50, debouncedAuthorSearchTerm);\r\n  const authors = authorsData?.authors || [];\r\n\r\n  // Автоматический фокус на поле поиска автора при открытии поповера\r\n  useEffect(() => {\r\n    if (openAuthorPopover && authorSearchInputRef.current) {\r\n      setTimeout(() => {\r\n        authorSearchInputRef.current?.focus();\r\n      }, 100);\r\n    }\r\n  }, [openAuthorPopover]);\r\n\r\n  // Установка выбранного автора при изменении value\r\n  useEffect(() => {\r\n    if (value && authors.length > 0) {\r\n      const author = authors.find(a => a.id === value);\r\n      if (author) {\r\n        setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n      }\r\n    } else {\r\n      setSelectedAuthor(null);\r\n    }\r\n  }, [value, authors]);\r\n\r\n  return (\r\n    <Popover open={openAuthorPopover} onOpenChange={setOpenAuthorPopover}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={openAuthorPopover}\r\n          className=\"w-full justify-between\"\r\n          disabled={disabled || isLoadingAuthors}\r\n        >\r\n          {isLoadingAuthors ? (\r\n            \"Авторлар йөкләнә...\"\r\n          ) : value ? (\r\n            selectedAuthor?.displayName || authorsData?.authors.find((author) => author.id === value)?.displayName || \"Автор сайланган\"\r\n          ) : (\r\n            \"Автор сайлагыз\"\r\n          )}\r\n          <div className=\"flex\">\r\n            {value && (\r\n              <div\r\n                className=\"h-4 w-4 mr-1 flex items-center justify-center cursor-pointer rounded-sm hover:bg-accent hover:text-accent-foreground\"\r\n                onClick={(e) => {\r\n                  e.stopPropagation();\r\n                  onChange(\"\");\r\n                  setSelectedAuthor(null);\r\n                  setOpenAuthorPopover(false);\r\n                }}\r\n                style={{ opacity: isLoadingAuthors ? 0.5 : 1, pointerEvents: isLoadingAuthors ? 'none' : 'auto' }}\r\n              >\r\n                <X className=\"h-3 w-3\" />\r\n              </div>\r\n            )}\r\n            <ChevronsUpDown className=\"ml-1 h-4 w-4 shrink-0 opacity-50\" />\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-full p-0\" align=\"start\">\r\n        <div className=\"flex flex-col w-full\">\r\n          <div className=\"flex items-center border-b px-3\">\r\n            <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\r\n            <input\r\n              ref={authorSearchInputRef}\r\n              className=\"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground\"\r\n              placeholder=\"Автор эзләү...\"\r\n              value={authorSearchTerm}\r\n              onChange={(e) => {\r\n                setAuthorSearchTerm(e.target.value);\r\n                setHighlightedIndex(0);\r\n              }}\r\n              onKeyDown={(e) => {\r\n                if (!authors.length) return;\r\n\r\n                if (e.key === \"ArrowDown\") {\r\n                  e.preventDefault();\r\n                  setHighlightedIndex((prev) =>\r\n                    prev < authors.length - 1 ? prev + 1 : prev\r\n                  );\r\n                } else if (e.key === \"ArrowUp\") {\r\n                  e.preventDefault();\r\n                  setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : 0));\r\n                } else if (e.key === \"Enter\" && highlightedIndex >= 0) {\r\n                  e.preventDefault();\r\n                  const author = authors[highlightedIndex];\r\n                  if (author) {\r\n                    const newAuthorId = author.id === value ? \"\" : author.id;\r\n                    onChange(newAuthorId);\r\n                    if (newAuthorId) {\r\n                      setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n                    } else {\r\n                      setSelectedAuthor(null);\r\n                    }\r\n                    setAuthorSearchTerm(\"\");\r\n                    setOpenAuthorPopover(false);\r\n                  }\r\n                } else if (e.key === \"Escape\") {\r\n                  e.preventDefault();\r\n                  setOpenAuthorPopover(false);\r\n                }\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"max-h-[300px] overflow-y-auto overflow-x-hidden\">\r\n            {isLoadingAuthors ? (\r\n              <div className=\"py-6 text-center text-sm\">Авторлар йөкләнә...</div>\r\n            ) : authors.length === 0 ? (\r\n              <div className=\"py-6 text-center text-sm\">Авторлар табылмады</div>\r\n            ) : (\r\n              <div className=\"p-1\">\r\n                {authors.map((author, index) => (\r\n                  <div\r\n                    key={author.id}\r\n                    className={cn(\r\n                      \"relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground\",\r\n                      (value === author.id || index === highlightedIndex) && \"bg-accent text-accent-foreground\"\r\n                    )}\r\n                    onClick={() => {\r\n                      const newAuthorId = author.id === value ? \"\" : author.id;\r\n                      onChange(newAuthorId);\r\n                      if (newAuthorId) {\r\n                        setSelectedAuthor({ id: author.id, displayName: author.displayName });\r\n                      } else {\r\n                        setSelectedAuthor(null);\r\n                      }\r\n                      setAuthorSearchTerm(\"\");\r\n                      setOpenAuthorPopover(false);\r\n                    }}\r\n                    onMouseEnter={() => setHighlightedIndex(index)}\r\n                  >\r\n                    <Check\r\n                      className={cn(\r\n                        \"mr-2 h-4 w-4\",\r\n                        value === author.id ? \"opacity-100\" : \"opacity-0\"\r\n                      )}\r\n                    />\r\n                    {author.displayName}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AARA;;;;;;;AAgBO,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAqB;;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAA8C;IACjG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAU,CAAC;IAClE,MAAM,uBAAuB,CAAA,GAAA,uWAAA,CAAA,SAAM,AAAD,EAAoB;IAEtD,gCAAgC;IAChC,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ;gDAAW;oBACvB,6BAA6B;gBAC/B;+CAAG;YAEH;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;KAAiB;IAErB,MAAM,EAAE,MAAM,WAAW,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE,GAAG,IAAI;IAC7E,MAAM,UAAU,aAAa,WAAW,EAAE;IAE1C,mEAAmE;IACnE,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,qBAAqB,qBAAqB,OAAO,EAAE;gBACrD;8CAAW;wBACT,qBAAqB,OAAO,EAAE;oBAChC;6CAAG;YACL;QACF;iCAAG;QAAC;KAAkB;IAEtB,kDAAkD;IAClD,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,SAAS,QAAQ,MAAM,GAAG,GAAG;gBAC/B,MAAM,SAAS,QAAQ,IAAI;qDAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAC1C,IAAI,QAAQ;oBACV,kBAAkB;wBAAE,IAAI,OAAO,EAAE;wBAAE,aAAa,OAAO,WAAW;oBAAC;gBACrE;YACF,OAAO;gBACL,kBAAkB;YACpB;QACF;iCAAG;QAAC;QAAO;KAAQ;IAEnB,qBACE,uYAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAmB,cAAc;;0BAC9C,uYAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,uYAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;oBACV,UAAU,YAAY;;wBAErB,mBACC,wBACE,QACF,gBAAgB,eAAe,aAAa,QAAQ,KAAK,CAAC,SAAW,OAAO,EAAE,KAAK,QAAQ,eAAe,oBAE1G;sCAEF,uYAAC;4BAAI,WAAU;;gCACZ,uBACC,uYAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,SAAS;wCACT,kBAAkB;wCAClB,qBAAqB;oCACvB;oCACA,OAAO;wCAAE,SAAS,mBAAmB,MAAM;wCAAG,eAAe,mBAAmB,SAAS;oCAAO;8CAEhG,cAAA,uYAAC,mRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAGjB,uYAAC,qTAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAIhC,uYAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAa,OAAM;0BAC3C,cAAA,uYAAC;oBAAI,WAAU;;sCACb,uYAAC;4BAAI,WAAU;;8CACb,uYAAC,6RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,uYAAC;oCACC,KAAK;oCACL,WAAU;oCACV,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC;wCACT,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCAClC,oBAAoB;oCACtB;oCACA,WAAW,CAAC;wCACV,IAAI,CAAC,QAAQ,MAAM,EAAE;wCAErB,IAAI,EAAE,GAAG,KAAK,aAAa;4CACzB,EAAE,cAAc;4CAChB,oBAAoB,CAAC,OACnB,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,IAAI;wCAE3C,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;4CAC9B,EAAE,cAAc;4CAChB,oBAAoB,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI;wCACvD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,oBAAoB,GAAG;4CACrD,EAAE,cAAc;4CAChB,MAAM,SAAS,OAAO,CAAC,iBAAiB;4CACxC,IAAI,QAAQ;gDACV,MAAM,cAAc,OAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,EAAE;gDACxD,SAAS;gDACT,IAAI,aAAa;oDACf,kBAAkB;wDAAE,IAAI,OAAO,EAAE;wDAAE,aAAa,OAAO,WAAW;oDAAC;gDACrE,OAAO;oDACL,kBAAkB;gDACpB;gDACA,oBAAoB;gDACpB,qBAAqB;4CACvB;wCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;4CAC7B,EAAE,cAAc;4CAChB,qBAAqB;wCACvB;oCACF;;;;;;;;;;;;sCAIJ,uYAAC;4BAAI,WAAU;sCACZ,iCACC,uYAAC;gCAAI,WAAU;0CAA2B;;;;;uCACxC,QAAQ,MAAM,KAAK,kBACrB,uYAAC;gCAAI,WAAU;0CAA2B;;;;;qDAE1C,uYAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,uYAAC;wCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kJACA,CAAC,UAAU,OAAO,EAAE,IAAI,UAAU,gBAAgB,KAAK;wCAEzD,SAAS;4CACP,MAAM,cAAc,OAAO,EAAE,KAAK,QAAQ,KAAK,OAAO,EAAE;4CACxD,SAAS;4CACT,IAAI,aAAa;gDACf,kBAAkB;oDAAE,IAAI,OAAO,EAAE;oDAAE,aAAa,OAAO,WAAW;gDAAC;4CACrE,OAAO;gDACL,kBAAkB;4CACpB;4CACA,oBAAoB;4CACpB,qBAAqB;wCACvB;wCACA,cAAc,IAAM,oBAAoB;;0DAExC,uYAAC,2RAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,OAAO,EAAE,GAAG,gBAAgB;;;;;;4CAGzC,OAAO,WAAW;;uCAxBd,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkClC;GAtKgB;;QAiB6C,wIAAA,CAAA,aAAU;;;KAjBvD", "debugId": null}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/components/works/works-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useWorks, useDeleteWork } from \"@/lib/hooks/use-works\";\r\nimport { formatDate } from \"@/lib/utils/format-date\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { toast } from \"sonner\";\r\nimport { Pagination } from \"@/components/ui/pagination\";\r\nimport { AuthorSelect } from \"@/components/authors/author-select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronsUpDown } from \"lucide-react\";\r\n\r\ninterface WorksTableProps {\r\n  initialAuthorId?: string;\r\n  onAuthorChange?: (authorId: string) => void;\r\n}\r\n\r\nexport function WorksTable({ initialAuthorId, onAuthorChange }: WorksTableProps) {\r\n  const router = useRouter();\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [pageSize, setPageSize] = useState(20);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(\"\");\r\n  const [workToDelete, setWorkToDelete] = useState<string | null>(null);\r\n  const [pageSizeDropdownOpen, setPageSizeDropdownOpen] = useState(false);\r\n  const [authorId, setAuthorId] = useState<string>(initialAuthorId || \"\");\r\n  const [debouncedAuthorId, setDebouncedAuthorId] = useState<string>(initialAuthorId || \"\");\r\n\r\n  const searchInputRef = useRef<HTMLInputElement>(null);\r\n  const pageSizeRef = useRef<HTMLDivElement>(null);\r\n  const deleteWork = useDeleteWork();\r\n\r\n  // Дебаунсинг для поиска\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedSearchTerm(searchTerm);\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [searchTerm]);\r\n\r\n  // Дебаунсинг для фильтра по автору\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedAuthorId(authorId);\r\n    }, 300);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [authorId]);\r\n\r\n  // Сброс страницы при изменении поискового запроса или фильтра по автору\r\n  useEffect(() => {\r\n    setCurrentPage(1);\r\n  }, [debouncedSearchTerm, debouncedAuthorId]);\r\n\r\n  // Удаляем эффект, который вызывал бесконечный цикл\r\n  // Теперь мы будем вызывать onAuthorChange только при явном изменении автора в компоненте AuthorSelect\r\n\r\n  // Получаем список произведений с учетом фильтров\r\n  const { data, isLoading, error, refetch } = useWorks(currentPage, pageSize, debouncedSearchTerm, debouncedAuthorId);\r\n\r\n  const works = data?.works || [];\r\n  const totalCount = data?.totalCount || 0;\r\n  const totalPages = Math.ceil(totalCount / pageSize);\r\n\r\n  if (isLoading) {\r\n    return <div className=\"text-center py-4\">Йөкләнә...</div>;\r\n  }\r\n\r\n  if (error) {\r\n    return <div className=\"text-center py-4 text-red-500\">Хата: {error.message}</div>;\r\n  }\r\n\r\n  const handleDelete = async () => {\r\n    if (workToDelete) {\r\n      try {\r\n        await deleteWork.mutateAsync(workToDelete);\r\n        toast.success(\"Әсәр уңышлы бетерелде\");\r\n        setWorkToDelete(null);\r\n\r\n        // Сохраняем фокус после обновления данных\r\n        const activeElement = document.activeElement;\r\n        await refetch();\r\n        if (activeElement === searchInputRef.current) {\r\n          searchInputRef.current?.focus();\r\n        }\r\n      } catch (error) {\r\n        toast.error(\"Хата: \" + (error instanceof Error ? error.message : \"Билгесез хата\"));\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto\">\r\n          <div className=\"flex flex-col md:flex-row gap-2 w-full\">\r\n            <Input\r\n              ref={searchInputRef}\r\n              placeholder=\"Эзләү...\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n              className=\"w-full sm:w-auto max-w-sm\"\r\n            />\r\n\r\n            <div className=\"relative w-full sm:w-auto max-w-sm\">\r\n              <AuthorSelect\r\n                value={authorId}\r\n                onChange={(newAuthorId) => {\r\n                  setAuthorId(newAuthorId);\r\n                  setCurrentPage(1); // Сбрасываем страницу при изменении фильтра\r\n\r\n                  // Вызываем onAuthorChange только при явном изменении автора\r\n                  if (onAuthorChange) {\r\n                    onAuthorChange(newAuthorId);\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-sm text-muted-foreground whitespace-nowrap\">Биттә күрсәтү:</span>\r\n            <div className=\"relative\" ref={pageSizeRef}>\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"w-16 h-9 px-3 flex justify-between items-center\"\r\n                onClick={() => setPageSizeDropdownOpen(!pageSizeDropdownOpen)}\r\n              >\r\n                {pageSize}\r\n                <ChevronsUpDown className=\"h-4 w-4 shrink-0 opacity-50\" />\r\n              </Button>\r\n\r\n              {pageSizeDropdownOpen && (\r\n                <div className=\"absolute top-full mt-1 w-16 rounded-md border bg-popover text-popover-foreground shadow-md z-[9999]\">\r\n                  <div className=\"p-1\">\r\n                    {[5, 10, 20, 50].map((size) => (\r\n                      <div\r\n                        key={size}\r\n                        className={cn(\r\n                          \"relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground\",\r\n                          pageSize === size && \"bg-accent text-accent-foreground\"\r\n                        )}\r\n                        onClick={() => {\r\n                          setPageSize(size);\r\n                          setCurrentPage(1);\r\n                          setPageSizeDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        {size}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <Button onClick={(e) => {\r\n          e.preventDefault();\r\n          e.stopPropagation();\r\n          // Если выбран автор, добавляем его в URL\r\n          const url = authorId\r\n            ? `/works/create?authorId=${encodeURIComponent(authorId)}`\r\n            : \"/works/create\";\r\n          router.push(url);\r\n        }}>\r\n          Яңа әсәр өстәү\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"border rounded-md\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Исем</TableHead>\r\n              <TableHead>Басылган көне</TableHead>\r\n              <TableHead>Үзгәртелгән көне</TableHead>\r\n              <TableHead className=\"text-right\">Гамәлләр</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {works?.length ? (\r\n              works.map((work) => (\r\n                <TableRow key={work.id}>\r\n                  <TableCell className=\"font-medium\">{work.title}</TableCell>\r\n                  <TableCell>{work.publishedDate ? formatDate(work.publishedDate) : \"-\"}</TableCell>\r\n                  <TableCell>{formatDate(work.modifiedDate)}</TableCell>\r\n                  <TableCell className=\"text-right\">\r\n                    <div className=\"flex justify-end gap-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          e.stopPropagation();\r\n                          // Извлекаем ID из полного ID работы (убираем префикс \"works/\")\r\n                          const workId = work.id.replace('works/', '');\r\n                          router.push(`/works/${workId}`);\r\n                        }}\r\n                      >\r\n                        Үзгәртү\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"destructive\"\r\n                        size=\"sm\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          e.stopPropagation();\r\n                          setWorkToDelete(work.id);\r\n                        }}\r\n                      >\r\n                        Бетерү\r\n                      </Button>\r\n                    </div>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell colSpan={4} className=\"text-center\">\r\n                  Әсәрләр табылмады\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      <Dialog open={!!workToDelete} onOpenChange={(open) => !open && setWorkToDelete(null)}>\r\n        <DialogContent>\r\n          <DialogHeader>\r\n            <DialogTitle>Әсәрне бетерергә телисезме?</DialogTitle>\r\n            <DialogDescription>\r\n              Бу гамәлне кире кайтарып булмый. Әсәр бетереләчәк.\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setWorkToDelete(null)}>\r\n              Юк\r\n            </Button>\r\n            <Button variant=\"destructive\" onClick={handleDelete} disabled={deleteWork.isPending}>\r\n              {deleteWork.isPending ? \"Бетерелә...\" : \"Әйе, бетерергә\"}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {totalPages > 1 && (\r\n        <Pagination\r\n          currentPage={currentPage}\r\n          totalPages={totalPages}\r\n          onPageChange={setCurrentPage}\r\n        />\r\n      )}\r\n\r\n      <div className=\"text-sm text-muted-foreground mt-2 text-center\">\r\n        Барлыгы {totalCount} әсәр, күрсәтелә {(currentPage - 1) * pageSize + 1}-{Math.min(currentPage * pageSize, totalCount)}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;AAmCO,SAAS,WAAW,EAAE,eAAe,EAAE,cAAc,EAAmB;;IAC7E,MAAM,SAAS,CAAA,GAAA,+UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAU,mBAAmB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,uWAAA,CAAA,WAAQ,AAAD,EAAU,mBAAmB;IAEtF,MAAM,iBAAiB,CAAA,GAAA,uWAAA,CAAA,SAAM,AAAD,EAAoB;IAChD,MAAM,cAAc,CAAA,GAAA,uWAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,aAAa,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;IAE/B,wBAAwB;IACxB,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,QAAQ;8CAAW;oBACvB,uBAAuB;gBACzB;6CAAG;YAEH;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;KAAW;IAEf,mCAAmC;IACnC,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,QAAQ;8CAAW;oBACvB,qBAAqB;gBACvB;6CAAG;YAEH;wCAAO,IAAM,aAAa;;QAC5B;+BAAG;QAAC;KAAS;IAEb,wEAAwE;IACxE,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;gCAAE;YACR,eAAe;QACjB;+BAAG;QAAC;QAAqB;KAAkB;IAE3C,mDAAmD;IACnD,sGAAsG;IAEtG,iDAAiD;IACjD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,UAAU,qBAAqB;IAEjG,MAAM,QAAQ,MAAM,SAAS,EAAE;IAC/B,MAAM,aAAa,MAAM,cAAc;IACvC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,IAAI,WAAW;QACb,qBAAO,uYAAC;YAAI,WAAU;sBAAmB;;;;;;IAC3C;IAEA,IAAI,OAAO;QACT,qBAAO,uYAAC;YAAI,WAAU;;gBAAgC;gBAAO,MAAM,OAAO;;;;;;;IAC5E;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,IAAI;gBACF,MAAM,WAAW,WAAW,CAAC;gBAC7B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,gBAAgB;gBAEhB,0CAA0C;gBAC1C,MAAM,gBAAgB,SAAS,aAAa;gBAC5C,MAAM;gBACN,IAAI,kBAAkB,eAAe,OAAO,EAAE;oBAC5C,eAAe,OAAO,EAAE;gBAC1B;YACF,EAAE,OAAO,OAAO;gBACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,eAAe;YAClF;QACF;IACF;IAEA,qBACE,uYAAC;QAAI,WAAU;;0BACb,uYAAC;gBAAI,WAAU;;kCACb,uYAAC;wBAAI,WAAU;;0CACb,uYAAC;gCAAI,WAAU;;kDACb,uYAAC,oIAAA,CAAA,QAAK;wCACJ,KAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;kDAGZ,uYAAC;wCAAI,WAAU;kDACb,cAAA,uYAAC,oJAAA,CAAA,eAAY;4CACX,OAAO;4CACP,UAAU,CAAC;gDACT,YAAY;gDACZ,eAAe,IAAI,4CAA4C;gDAE/D,4DAA4D;gDAC5D,IAAI,gBAAgB;oDAClB,eAAe;gDACjB;4CACF;;;;;;;;;;;;;;;;;0CAKN,uYAAC;gCAAI,WAAU;;kDACb,uYAAC;wCAAK,WAAU;kDAAkD;;;;;;kDAClE,uYAAC;wCAAI,WAAU;wCAAW,KAAK;;0DAC7B,uYAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,wBAAwB,CAAC;;oDAEvC;kEACD,uYAAC,qTAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;4CAG3B,sCACC,uYAAC;gDAAI,WAAU;0DACb,cAAA,uYAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAG;wDAAI;wDAAI;qDAAG,CAAC,GAAG,CAAC,CAAC,qBACpB,uYAAC;4DAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kJACA,aAAa,QAAQ;4DAEvB,SAAS;gEACP,YAAY;gEACZ,eAAe;gEACf,wBAAwB;4DAC1B;sEAEC;2DAXI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAoBrB,uYAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,CAAC;4BAChB,EAAE,cAAc;4BAChB,EAAE,eAAe;4BACjB,yCAAyC;4BACzC,MAAM,MAAM,WACR,CAAC,uBAAuB,EAAE,mBAAmB,WAAW,GACxD;4BACJ,OAAO,IAAI,CAAC;wBACd;kCAAG;;;;;;;;;;;;0BAKL,uYAAC;gBAAI,WAAU;0BACb,cAAA,uYAAC,oIAAA,CAAA,QAAK;;sCACJ,uYAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,uYAAC,oIAAA,CAAA,WAAQ;;kDACP,uYAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uYAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uYAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,uYAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;;;;;;;;;;;;sCAGtC,uYAAC,oIAAA,CAAA,YAAS;sCACP,OAAO,SACN,MAAM,GAAG,CAAC,CAAC,qBACT,uYAAC,oIAAA,CAAA,WAAQ;;sDACP,uYAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,KAAK,KAAK;;;;;;sDAC9C,uYAAC,oIAAA,CAAA,YAAS;sDAAE,KAAK,aAAa,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,aAAa,IAAI;;;;;;sDAClE,uYAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD,EAAE,KAAK,YAAY;;;;;;sDACxC,uYAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,uYAAC;gDAAI,WAAU;;kEACb,uYAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,EAAE,eAAe;4DACjB,+DAA+D;4DAC/D,MAAM,SAAS,KAAK,EAAE,CAAC,OAAO,CAAC,UAAU;4DACzC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ;wDAChC;kEACD;;;;;;kEAGD,uYAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,EAAE,eAAe;4DACjB,gBAAgB,KAAK,EAAE;wDACzB;kEACD;;;;;;;;;;;;;;;;;;mCA3BQ,KAAK,EAAE;;;;0DAmCxB,uYAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,uYAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzD,uYAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM,CAAC,CAAC;gBAAc,cAAc,CAAC,OAAS,CAAC,QAAQ,gBAAgB;0BAC7E,cAAA,uYAAC,qIAAA,CAAA,gBAAa;;sCACZ,uYAAC,qIAAA,CAAA,eAAY;;8CACX,uYAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,uYAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,uYAAC,qIAAA,CAAA,eAAY;;8CACX,uYAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,gBAAgB;8CAAO;;;;;;8CAGhE,uYAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAc,SAAS;oCAAc,UAAU,WAAW,SAAS;8CAChF,WAAW,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAM/C,aAAa,mBACZ,uYAAC,yIAAA,CAAA,aAAU;gBACT,aAAa;gBACb,YAAY;gBACZ,cAAc;;;;;;0BAIlB,uYAAC;gBAAI,WAAU;;oBAAiD;oBACrD;oBAAW;oBAAkB,CAAC,cAAc,CAAC,IAAI,WAAW;oBAAE;oBAAE,KAAK,GAAG,CAAC,cAAc,UAAU;;;;;;;;;;;;;AAIlH;GApPgB;;QACC,+UAAA,CAAA,YAAS;QAYL,sIAAA,CAAA,gBAAa;QA6BY,sIAAA,CAAA,WAAQ;;;KA1CtC", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/lib/utils/use-dynamic-title.ts"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useRef } from \"react\";\n\n/**\n * Хук для динамического изменения заголовка страницы в клиентских компонентах\n *\n * @param title Заголовок страницы\n * @param suffix Суффикс заголовка (по умолчанию \"Шигърият.ру Админ\")\n */\nexport function useDynamicTitle(title: string | null | undefined, suffix: string = \"Шигърият.ру Админ\") {\n  // Используем ref для хранения предыдущего заголовка\n  const originalTitleRef = useRef<string | null>(null);\n\n  // Используем ref для отслеживания, был ли уже установлен заголовок\n  const isMountedRef = useRef(false);\n\n  useEffect(() => {\n    // Пропускаем выполнение на сервере\n    if (typeof document === 'undefined') return;\n\n    // Сохраняем оригинальный заголовок только при первом рендере\n    if (!isMountedRef.current) {\n      originalTitleRef.current = document.title;\n      isMountedRef.current = true;\n    }\n\n    // Не меняем заголовок, если title не определен\n    if (!title) return;\n\n    // Устанавливаем новый заголовок\n    const newTitle = suffix ? `${title} | ${suffix}` : title;\n\n    // Меняем заголовок только если он отличается от текущего\n    if (document.title !== newTitle) {\n      document.title = newTitle;\n    }\n\n    // Восстанавливаем оригинальный заголовок при размонтировании компонента\n    return () => {\n      if (originalTitleRef.current) {\n        document.title = originalTitleRef.current;\n      }\n    };\n  }, [title, suffix]);\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAUO,SAAS,gBAAgB,KAAgC,EAAE,SAAiB,mBAAmB;;IACpG,oDAAoD;IACpD,MAAM,mBAAmB,CAAA,GAAA,uWAAA,CAAA,SAAM,AAAD,EAAiB;IAE/C,mEAAmE;IACnE,MAAM,eAAe,CAAA,GAAA,uWAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,uWAAA,CAAA,YAAS,AAAD;qCAAE;YACR,mCAAmC;YACnC,IAAI,OAAO,aAAa,aAAa;YAErC,6DAA6D;YAC7D,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,iBAAiB,OAAO,GAAG,SAAS,KAAK;gBACzC,aAAa,OAAO,GAAG;YACzB;YAEA,+CAA+C;YAC/C,IAAI,CAAC,OAAO;YAEZ,gCAAgC;YAChC,MAAM,WAAW,SAAS,GAAG,MAAM,GAAG,EAAE,QAAQ,GAAG;YAEnD,yDAAyD;YACzD,IAAI,SAAS,KAAK,KAAK,UAAU;gBAC/B,SAAS,KAAK,GAAG;YACnB;YAEA,wEAAwE;YACxE;6CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,SAAS,KAAK,GAAG,iBAAiB,OAAO;oBAC3C;gBACF;;QACF;oCAAG;QAAC;QAAO;KAAO;AACpB;GAnCgB", "debugId": null}}, {"offset": {"line": 2223, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Projects/shigriyat/admin/src/app/%28admin%29/works/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { WorksTable } from \"@/components/works/works-table\";\nimport { useSearchParams, useRouter, usePathname } from \"next/navigation\";\nimport { useCallback, useMemo } from \"react\";\nimport { useDynamicTitle } from \"@/lib/utils/use-dynamic-title\";\n\nexport default function WorksListPage() {\n  const searchParams = useSearchParams();\n  const router = useRouter();\n  const pathname = usePathname();\n  const authorId = searchParams.get(\"authorId\");\n\n  // Мемоизируем заголовок, чтобы он не менялся при каждом рендере\n  const pageTitle = useMemo(() => {\n    return authorId ? \"Автор әсәрләре\" : \"Әсәрләр\";\n  }, [authorId]);\n\n  // Устанавливаем заголовок страницы\n  useDynamicTitle(pageTitle);\n\n  // Используем useCallback для предотвращения лишних перерендеров\n  const handleAuthorChange = useCallback((newAuthorId: string) => {\n    // Создаем новый объект URLSearchParams вместо изменения существующего\n    const params = new URLSearchParams();\n\n    // Сохраняем все существующие параметры, кроме authorId\n    searchParams.forEach((value, key) => {\n      if (key !== \"authorId\") {\n        params.set(key, value);\n      }\n    });\n\n    // Добавляем новый authorId, если он есть\n    if (newAuthorId) {\n      params.set(\"authorId\", newAuthorId);\n    }\n\n    // Используем реплейс вместо пуш\n    const url = params.toString() ? `${pathname}?${params.toString()}` : pathname;\n    router.replace(url, { scroll: false });\n  }, [pathname, router, searchParams]);\n\n  return (\n    <div className=\"container mx-auto py-10\">\n      <h1 className=\"text-3xl font-bold mb-8\">Әсәрләр</h1>\n      <WorksTable initialAuthorId={authorId || undefined} onAuthorChange={handleAuthorChange} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,+UAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,+UAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,+UAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,aAAa,GAAG,CAAC;IAElC,gEAAgE;IAChE,MAAM,YAAY,CAAA,GAAA,uWAAA,CAAA,UAAO,AAAD;4CAAE;YACxB,OAAO,WAAW,mBAAmB;QACvC;2CAAG;QAAC;KAAS;IAEb,mCAAmC;IACnC,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD,EAAE;IAEhB,gEAAgE;IAChE,MAAM,qBAAqB,CAAA,GAAA,uWAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACtC,sEAAsE;YACtE,MAAM,SAAS,IAAI;YAEnB,uDAAuD;YACvD,aAAa,OAAO;iEAAC,CAAC,OAAO;oBAC3B,IAAI,QAAQ,YAAY;wBACtB,OAAO,GAAG,CAAC,KAAK;oBAClB;gBACF;;YAEA,yCAAyC;YACzC,IAAI,aAAa;gBACf,OAAO,GAAG,CAAC,YAAY;YACzB;YAEA,gCAAgC;YAChC,MAAM,MAAM,OAAO,QAAQ,KAAK,GAAG,SAAS,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;YACrE,OAAO,OAAO,CAAC,KAAK;gBAAE,QAAQ;YAAM;QACtC;wDAAG;QAAC;QAAU;QAAQ;KAAa;IAEnC,qBACE,uYAAC;QAAI,WAAU;;0BACb,uYAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,uYAAC,gJAAA,CAAA,aAAU;gBAAC,iBAAiB,YAAY;gBAAW,gBAAgB;;;;;;;;;;;;AAG1E;GA1CwB;;QACD,+UAAA,CAAA,kBAAe;QACrB,+UAAA,CAAA,YAAS;QACP,+UAAA,CAAA,cAAW;QAS5B,iJAAA,CAAA,kBAAe;;;KAZO", "debugId": null}}]}